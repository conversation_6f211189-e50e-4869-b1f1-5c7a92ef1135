import React, { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  TouchableOpacity,
} from "react-native";
import { Text, Surface, Chip, Divider, Button } from "react-native-paper";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";
import { <PERSON><PERSON>hart, PieChart } from "react-native-chart-kit";
import { Dimensions } from "react-native";

const screenWidth = Dimensions.get("window").width;

const ReportScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { t } = useLanguage();
  const { isDarkMode, colors } = useTheme();
  const [activeTab, setActiveTab] = useState("operations"); // operations, finance
  const [operationsSubTab, setOperationsSubTab] = useState("room"); // room, building
  const [financeSubTab, setFinanceSubTab] = useState("revenue"); // revenue, expenses
  const [roomFilter, setRoomFilter] = useState("all"); // all, overdue, paid, repair, empty

  const roomOccupancyData = {
    labels: ["09 May", "10 May", "11 May", "12 May", "13 May", "14 May"],
    datasets: [
      {
        data: [42, 35, 28, 32, 28, 25],
        color: () => "#70C4D7", // Primary color
        strokeWidth: 2,
      },
    ],
  };

  const revenueData = {
    labels: ["09 May", "10 May", "11 May", "12 May", "13 May", "14 May"],
    datasets: [
      {
        data: [45, 28, 30, 15, 20, 12],
        color: () => "#70C4D7", // Primary color
        strokeWidth: 2,
      },
    ],
  };

  const expensesData = [
    {
      name: t("report.staffSalary"),
      population: 12,
      color: "#4285F4",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.maintenance"),
      population: 7,
      color: "#F4B400",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
    {
      name: t("report.renovation"),
      population: 6,
      color: "#0F9D58",
      legendFontColor: isDarkMode ? colors.TEXT_PRIMARY : "#7F7F7F",
      legendFontSize: 15,
    },
  ];

  const chartConfig = {
    backgroundGradientFrom: isDarkMode ? colors.CARD_BACKGROUND : "#fff",
    backgroundGradientTo: isDarkMode ? colors.CARD_BACKGROUND : "#fff",
    decimalPlaces: 0,
    color: () => (isDarkMode ? colors.TEXT_PRIMARY : "#333"),
    labelColor: () => (isDarkMode ? colors.TEXT_SECONDARY : "#999"),
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: "6",
      strokeWidth: "2",
      stroke: "#70C4D7",
    },
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <MaterialCommunityIcons
          name="chevron-left"
          size={30}
          color={colors.WHITE}
        />
      </TouchableOpacity>
      <Text style={styles.headerTitle}>{t("report.statistics")}</Text>
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "operations" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("operations")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "operations" && styles.activeTabText,
            ]}
          >
            {t("report.operations")}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.tabButton,
            activeTab === "finance" && styles.activeTabButton,
          ]}
          onPress={() => setActiveTab("finance")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "finance" && styles.activeTabText,
            ]}
          >
            {t("report.finance")}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderOperationsSubTabs = () => (
    <View style={styles.subTabContainer}>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          operationsSubTab === "room" && styles.activeSubTabButton,
        ]}
        onPress={() => setOperationsSubTab("room")}
      >
        <Text
          style={[
            styles.subTabText,
            operationsSubTab === "room" && styles.activeSubTabText,
          ]}
        >
          {t("report.room")}
        </Text>
        {operationsSubTab === "room" && <View style={styles.subTabIndicator} />}
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          operationsSubTab === "building" && styles.activeSubTabButton,
        ]}
        onPress={() => setOperationsSubTab("building")}
      >
        <Text
          style={[
            styles.subTabText,
            operationsSubTab === "building" && styles.activeSubTabText,
          ]}
        >
          {t("report.building")}
        </Text>
        {operationsSubTab === "building" && (
          <View style={styles.subTabIndicator} />
        )}
      </TouchableOpacity>
    </View>
  );

  const renderFinanceSubTabs = () => (
    <View style={styles.subTabContainer}>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          financeSubTab === "revenue" && styles.activeSubTabButton,
        ]}
        onPress={() => setFinanceSubTab("revenue")}
      >
        <Text
          style={[
            styles.subTabText,
            financeSubTab === "revenue" && styles.activeSubTabText,
          ]}
        >
          {t("report.revenue")}
        </Text>
        {financeSubTab === "revenue" && <View style={styles.subTabIndicator} />}
      </TouchableOpacity>
      <TouchableOpacity
        style={[
          styles.subTabButton,
          financeSubTab === "expenses" && styles.activeSubTabButton,
        ]}
        onPress={() => setFinanceSubTab("expenses")}
      >
        <Text
          style={[
            styles.subTabText,
            financeSubTab === "expenses" && styles.activeSubTabText,
          ]}
        >
          {t("report.expenses")}
        </Text>
        {financeSubTab === "expenses" && (
          <View style={styles.subTabIndicator} />
        )}
      </TouchableOpacity>
    </View>
  );

  const renderRoomManagement = () => (
    <ScrollView style={styles.container}>
      <Surface
        style={[
          styles.dateRangeCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <Text style={{ color: colors.TEXT_PRIMARY }}>
          {t("report.datePeriod")} 01/04 - 24/04
        </Text>
        <Text style={{ color: colors.TEXT_SECONDARY, fontSize: 12 }}>
          {t("report.lastUpdated")} 20:25
        </Text>
        <View style={styles.datePickerButton}>
          <Text style={{ color: colors.TEXT_PRIMARY }}>{t("report.date")}</Text>
          <MaterialCommunityIcons
            name="chevron-down"
            size={20}
            color={colors.TEXT_PRIMARY}
          />
        </View>
      </Surface>

      <Surface
        style={[
          styles.sectionCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
          {t("report.roomManagement")}
        </Text>

        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.statusFiltersContainer}
        >
          <View style={styles.statusFilters}>
            <Chip
              selected={roomFilter === "all"}
              onPress={() => setRoomFilter("all")}
              style={[
                styles.statusFilter,
                roomFilter === "all" && { backgroundColor: "#70C4D7" },
              ]}
              textStyle={[
                {
                  color:
                    roomFilter === "all" ? colors.WHITE : colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("report.all")}
            </Chip>
            <Chip
              selected={roomFilter === "overdue"}
              onPress={() => setRoomFilter("overdue")}
              style={[
                styles.statusFilter,
                roomFilter === "overdue" && { backgroundColor: "#70C4D7" },
              ]}
              textStyle={[
                {
                  color:
                    roomFilter === "overdue"
                      ? colors.WHITE
                      : colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("report.overdue")}
            </Chip>
            <Chip
              selected={roomFilter === "paid"}
              onPress={() => setRoomFilter("paid")}
              style={[
                styles.statusFilter,
                roomFilter === "paid" && { backgroundColor: "#70C4D7" },
              ]}
              textStyle={[
                {
                  color:
                    roomFilter === "paid" ? colors.WHITE : colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("report.paid")}
            </Chip>
            <Chip
              selected={roomFilter === "repair"}
              onPress={() => setRoomFilter("repair")}
              style={[
                styles.statusFilter,
                roomFilter === "repair" && { backgroundColor: "#70C4D7" },
              ]}
              textStyle={[
                {
                  color:
                    roomFilter === "repair"
                      ? colors.WHITE
                      : colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("report.repair")}
            </Chip>
            <Chip
              selected={roomFilter === "empty"}
              onPress={() => setRoomFilter("empty")}
              style={[
                styles.statusFilter,
                roomFilter === "empty" && { backgroundColor: "#70C4D7" },
              ]}
              textStyle={[
                {
                  color:
                    roomFilter === "empty" ? colors.WHITE : colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("report.empty")}
            </Chip>
          </View>
        </ScrollView>

        <View style={styles.roomCountContainer}>
          <MaterialCommunityIcons name="door" size={24} color="#70C4D7" />
          <Text style={[styles.roomCount, { color: colors.TEXT_PRIMARY }]}>
            40
          </Text>
          <Text
            style={[styles.roomCountLabel, { color: colors.TEXT_SECONDARY }]}
          >
            {t("report.rooms")}
          </Text>
          <Button
            mode="contained"
            style={styles.truyVanButton}
            labelStyle={styles.truyVanButtonText}
          >
            {t("report.query")}
          </Button>
        </View>

        <View style={styles.floorContainer}>
          <Text style={[styles.floorTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("room.floor")} 1
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.roomGrid}>
              <View style={[styles.roomItem, { backgroundColor: "#70C4D7" }]}>
                <Text style={styles.roomNumber}>A101</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#70C4D7" }]}>
                <Text style={styles.roomNumber}>A102</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#FF5722" }]}>
                <Text style={styles.roomNumber}>A103</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#CCCCCC" }]}>
                <Text style={styles.roomNumber}>A104</Text>
              </View>
            </View>
          </ScrollView>
        </View>

        <View style={styles.floorContainer}>
          <Text style={[styles.floorTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("room.floor")} 2
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.roomGrid}>
              <View style={[styles.roomItem, { backgroundColor: "#000000" }]}>
                <Text style={styles.roomNumber}>B101</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#FF5722" }]}>
                <Text style={styles.roomNumber}>B102</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#FF5722" }]}>
                <Text style={styles.roomNumber}>B103</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#70C4D7" }]}>
                <Text style={styles.roomNumber}>B104</Text>
              </View>
            </View>
          </ScrollView>
        </View>

        <View style={styles.floorContainer}>
          <Text style={[styles.floorTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("room.floor")} 3
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.roomGrid}>
              <View style={[styles.roomItem, { backgroundColor: "#CCCCCC" }]}>
                <Text style={styles.roomNumber}>C101</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#70C4D7" }]}>
                <Text style={styles.roomNumber}>C102</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#000000" }]}>
                <Text style={styles.roomNumber}>C103</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#CCCCCC" }]}>
                <Text style={styles.roomNumber}>C104</Text>
              </View>
            </View>
          </ScrollView>
        </View>

        <View style={styles.floorContainer}>
          <Text style={[styles.floorTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("room.floor")} 4
          </Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.roomGrid}>
              <View style={[styles.roomItem, { backgroundColor: "#70C4D7" }]}>
                <Text style={styles.roomNumber}>D101</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#000000" }]}>
                <Text style={styles.roomNumber}>D102</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#FF5722" }]}>
                <Text style={styles.roomNumber}>D103</Text>
              </View>
              <View style={[styles.roomItem, { backgroundColor: "#CCCCCC" }]}>
                <Text style={styles.roomNumber}>D104</Text>
              </View>
            </View>
          </ScrollView>
        </View>
      </Surface>

      <Surface
        style={[
          styles.sectionCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons
            name="account-group"
            size={24}
            color="#70C4D7"
          />
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.tenantManagement")}
          </Text>
        </View>
        <Text
          style={[styles.tenantListLabel, { color: colors.TEXT_SECONDARY }]}
        >
          {t("report.tenantInfo")}
        </Text>

        <View style={styles.tenantList}>
          <View style={styles.tenantItem}>
            <Text style={[styles.tenantRoom, { color: colors.TEXT_PRIMARY }]}>
              D201 - Nguyễn Văn A
            </Text>
            <Text style={[styles.tenantStatus, { color: "#FF5722" }]}>
              {t("report.expired")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.tenantItem}>
            <Text style={[styles.tenantRoom, { color: colors.TEXT_PRIMARY }]}>
              A105 - Trần Văn B
            </Text>
            <Text style={[styles.tenantStatus, { color: "#FFC107" }]}>
              {t("report.expiringSoon")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.tenantItem}>
            <Text style={[styles.tenantRoom, { color: colors.TEXT_PRIMARY }]}>
              B304 - Hoàng Văn P
            </Text>
            <Text style={[styles.tenantStatus, { color: "#4CAF50" }]}>
              {t("report.renewed")}
            </Text>
          </View>
        </View>
      </Surface>
    </ScrollView>
  );

  const renderBuildingTab = () => (
    <ScrollView style={styles.container}>
      <Surface
        style={[
          styles.overviewCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.overviewHeader}>
          <MaterialCommunityIcons name="chart-bar" size={24} color="#70C4D7" />
          <Text style={[styles.overviewTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.overview")}
          </Text>
        </View>

        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.TEXT_PRIMARY }]}>
              12
            </Text>
            <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>
              {t("report.rented")}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.TEXT_PRIMARY }]}>
              8
            </Text>
            <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>
              {t("report.empty")}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.TEXT_PRIMARY }]}>
              3
            </Text>
            <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>
              {t("report.underRepair")}
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text style={[styles.statValue, { color: colors.TEXT_PRIMARY }]}>
              5
            </Text>
            <Text style={[styles.statLabel, { color: colors.TEXT_SECONDARY }]}>
              {t("report.movingOut")}
            </Text>
          </View>
        </View>

        <Text style={[styles.chartTitle, { color: colors.TEXT_PRIMARY }]}>
          {t("report.chart")}
        </Text>

        <View style={styles.chartFilterContainer}>
          <Text style={{ color: colors.TEXT_PRIMARY }}>{t("report.date")}</Text>
          <MaterialCommunityIcons
            name="chevron-down"
            size={20}
            color={colors.TEXT_PRIMARY}
          />
          <MaterialCommunityIcons
            name="chart-line"
            size={20}
            color={colors.TEXT_PRIMARY}
            style={{ marginLeft: 16 }}
          />
          <MaterialCommunityIcons
            name="chevron-down"
            size={20}
            color={colors.TEXT_PRIMARY}
          />
        </View>

        <LineChart
          data={roomOccupancyData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
        />

        <View style={styles.legendContainer}>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#70C4D7" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.rented")}
            </Text>
            <Text style={{ color: colors.TEXT_PRIMARY, marginLeft: "auto" }}>
              12
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                width: 50,
                textAlign: "right",
              }}
            >
              62.5{t("report.percentValue")}
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#FFC107" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.empty")}
            </Text>
            <Text style={{ color: colors.TEXT_PRIMARY, marginLeft: "auto" }}>
              7
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                width: 50,
                textAlign: "right",
              }}
            >
              25{t("report.percentValue")}
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#4CAF50" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.underRepair")}
            </Text>
            <Text style={{ color: colors.TEXT_PRIMARY, marginLeft: "auto" }}>
              6
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                width: 50,
                textAlign: "right",
              }}
            >
              12.5{t("report.percentValue")}
            </Text>
          </View>
        </View>
      </Surface>

      <Surface
        style={[
          styles.maintenanceCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.sectionTitleContainer}>
          <MaterialCommunityIcons name="tools" size={24} color="#70C4D7" />
          <Text style={[styles.sectionTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.maintenanceSchedule")}
          </Text>
        </View>

        <View style={styles.maintenanceList}>
          <View style={styles.maintenanceItem}>
            <Text
              style={[styles.maintenanceRoom, { color: colors.TEXT_PRIMARY }]}
            >
              C103 - 15/04/2025
            </Text>
            <Text style={[styles.maintenanceStatus, { color: "#4CAF50" }]}>
              {t("report.completed")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.maintenanceItem}>
            <Text
              style={[styles.maintenanceRoom, { color: colors.TEXT_PRIMARY }]}
            >
              A104 - 12/04/2025
            </Text>
            <Text style={[styles.maintenanceStatus, { color: "#FFC107" }]}>
              {t("report.inProgress")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.maintenanceItem}>
            <Text
              style={[styles.maintenanceRoom, { color: colors.TEXT_PRIMARY }]}
            >
              B304 - 15/04/2025
            </Text>
            <Text style={[styles.maintenanceStatus, { color: "#FFC107" }]}>
              {t("report.inProgress")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.maintenanceItem}>
            <Text
              style={[styles.maintenanceRoom, { color: colors.TEXT_PRIMARY }]}
            >
              B304 - 15/04/2025
            </Text>
            <Text style={[styles.maintenanceStatus, { color: "#FFC107" }]}>
              {t("report.inProgress")}
            </Text>
          </View>
          <Divider style={styles.divider} />

          <View style={styles.maintenanceItem}>
            <Text
              style={[styles.maintenanceRoom, { color: colors.TEXT_PRIMARY }]}
            >
              B304 - 15/04/2025
            </Text>
            <Text style={[styles.maintenanceStatus, { color: "#FFC107" }]}>
              {t("report.inProgress")}
            </Text>
          </View>
        </View>
      </Surface>
    </ScrollView>
  );

  const renderRevenueTab = () => (
    <ScrollView style={styles.container}>
      <Surface
        style={[
          styles.revenueCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.revenueHeader}>
          <View style={styles.revenueHeaderLeft}>
            <MaterialCommunityIcons
              name="cash-multiple"
              size={24}
              color="#70C4D7"
            />
            <Text style={[styles.revenueTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.totalRevenue")}
            </Text>
          </View>
          <MaterialCommunityIcons
            name="chevron-right"
            size={24}
            color={colors.TEXT_PRIMARY}
          />
        </View>
        <Text style={[styles.revenueAmount, { color: "#4CAF50" }]}>
          50.000.000 VND
        </Text>
        <Text style={[styles.revenuePeriod, { color: colors.TEXT_SECONDARY }]}>
          32tr trong tháng trước
        </Text>
        <View style={styles.percentageContainer}>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons name="arrow-up" size={16} color="#4CAF50" />
            <Text style={{ color: "#4CAF50" }}>20%</Text>
          </View>
        </View>
      </Surface>

      <View style={styles.revenueGridContainer}>
        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>{t("report.revenue")} TB</Text>
          <Text style={styles.revenueGridValue}>2.5tr VND</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons name="arrow-up" size={16} color="#FFFFFF" />
            <Text style={{ color: "#FFFFFF" }}>+11.01%</Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#333333" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>
            {t("report.newContracts")}
          </Text>
          <Text style={styles.revenueGridValue}>3 hợp đồng</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons
              name="arrow-down"
              size={16}
              color="#FFFFFF"
            />
            <Text style={{ color: "#FFFFFF" }}>-0.03%</Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>
            {t("report.onTimePayment")}
          </Text>
          <Text style={styles.revenueGridValue}>80%</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons name="arrow-up" size={16} color="#FFFFFF" />
            <Text style={{ color: "#FFFFFF" }}>+11.01%</Text>
          </View>
        </Surface>

        <Surface
          style={[styles.revenueGridItem, { backgroundColor: "#4CAF50" }]}
          elevation={1}
        >
          <Text style={styles.revenueGridTitle}>
            {t("report.reservedRooms")}
          </Text>
          <Text style={styles.revenueGridValue}>4 phòng</Text>
          <View style={styles.percentageBadge}>
            <MaterialCommunityIcons name="arrow-up" size={16} color="#FFFFFF" />
            <Text style={{ color: "#FFFFFF" }}>+11.01%</Text>
          </View>
        </Surface>
      </View>

      <Surface
        style={[styles.chartCard, { backgroundColor: colors.CARD_BACKGROUND }]}
        elevation={1}
      >
        <Text style={[styles.chartTitle, { color: colors.TEXT_PRIMARY }]}>
          {t("report.chart")}
        </Text>

        <View style={styles.chartFilterContainer}>
          <Text style={{ color: colors.TEXT_PRIMARY }}>{t("report.date")}</Text>
          <MaterialCommunityIcons
            name="chevron-down"
            size={20}
            color={colors.TEXT_PRIMARY}
          />
          <MaterialCommunityIcons
            name="chart-line"
            size={20}
            color={colors.TEXT_PRIMARY}
            style={{ marginLeft: 16 }}
          />
          <MaterialCommunityIcons
            name="chevron-down"
            size={20}
            color={colors.TEXT_PRIMARY}
          />
        </View>

        <LineChart
          data={revenueData}
          width={screenWidth - 64}
          height={220}
          chartConfig={chartConfig}
          bezier
          style={styles.chart}
        />

        <View style={styles.legendContainer}>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#70C4D7" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.revenue")} TB
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                marginLeft: "auto",
                width: 50,
                textAlign: "right",
              }}
            >
              62.5{t("report.percentValue")}
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#FFC107" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.newContracts")}
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                marginLeft: "auto",
                width: 50,
                textAlign: "right",
              }}
            >
              25{t("report.percentValue")}
            </Text>
          </View>
          <View style={styles.legendItem}>
            <View
              style={[styles.legendColor, { backgroundColor: "#4CAF50" }]}
            />
            <Text style={{ color: colors.TEXT_PRIMARY }}>
              {t("report.onTimePayment")}
            </Text>
            <Text
              style={{
                color: colors.TEXT_PRIMARY,
                marginLeft: "auto",
                width: 50,
                textAlign: "right",
              }}
            >
              12.5{t("report.percentValue")}
            </Text>
          </View>
        </View>
      </Surface>
    </ScrollView>
  );

  const renderExpensesTab = () => (
    <ScrollView style={styles.container}>
      <Surface
        style={[
          styles.expenseCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.expenseHeader}>
          <View style={styles.expenseHeaderLeft}>
            <MaterialCommunityIcons
              name="account-cash"
              size={24}
              color="#70C4D7"
            />
            <Text style={[styles.expenseTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.staffSalary")}
            </Text>
          </View>
          <MaterialCommunityIcons
            name="chevron-right"
            size={24}
            color={colors.TEXT_PRIMARY}
          />
        </View>
        <Text style={[styles.expenseAmount, { color: "#FF5722" }]}>
          12.000.000 VND
        </Text>
        <Text style={[styles.expensePeriod, { color: colors.TEXT_SECONDARY }]}>
          10tr trong tháng trước
        </Text>
        <View style={styles.percentageContainer}>
          <View
            style={[styles.percentageBadge, { backgroundColor: "#FFEBEE" }]}
          >
            <MaterialCommunityIcons name="arrow-up" size={16} color="#FF5722" />
            <Text style={{ color: "#FF5722" }}>20%</Text>
          </View>
        </View>
      </Surface>

      <Surface
        style={[
          styles.expenseCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.expenseHeader}>
          <View style={styles.expenseHeaderLeft}>
            <MaterialCommunityIcons name="tools" size={24} color="#70C4D7" />
            <Text style={[styles.expenseTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.maintenance")}
            </Text>
          </View>
          <MaterialCommunityIcons
            name="chevron-right"
            size={24}
            color={colors.TEXT_PRIMARY}
          />
        </View>
        <Text style={[styles.expenseAmount, { color: "#FF5722" }]}>
          7.000.000 VND
        </Text>
        <Text style={[styles.expensePeriod, { color: colors.TEXT_SECONDARY }]}>
          2tr trong tháng trước
        </Text>
        <View style={styles.percentageContainer}>
          <View
            style={[styles.percentageBadge, { backgroundColor: "#FFEBEE" }]}
          >
            <MaterialCommunityIcons name="arrow-up" size={16} color="#FF5722" />
            <Text style={{ color: "#FF5722" }}>20%</Text>
          </View>
        </View>
      </Surface>

      <Surface
        style={[
          styles.expenseCard,
          { backgroundColor: colors.CARD_BACKGROUND },
        ]}
        elevation={1}
      >
        <View style={styles.expenseHeader}>
          <View style={styles.expenseHeaderLeft}>
            <MaterialCommunityIcons
              name="home-city"
              size={24}
              color="#70C4D7"
            />
            <Text style={[styles.expenseTitle, { color: colors.TEXT_PRIMARY }]}>
              {t("report.renovation")}
            </Text>
          </View>
          <MaterialCommunityIcons
            name="chevron-right"
            size={24}
            color={colors.TEXT_PRIMARY}
          />
        </View>
        <Text style={[styles.expenseAmount, { color: "#4CAF50" }]}>
          6.000.000 VND
        </Text>
        <Text style={[styles.expensePeriod, { color: colors.TEXT_SECONDARY }]}>
          12tr trong tháng trước
        </Text>
        <View style={styles.percentageContainer}>
          <View
            style={[styles.percentageBadge, { backgroundColor: "#E8F5E9" }]}
          >
            <MaterialCommunityIcons
              name="arrow-down"
              size={16}
              color="#4CAF50"
            />
            <Text style={{ color: "#4CAF50" }}>60%</Text>
          </View>
        </View>
      </Surface>

      <Surface
        style={[styles.chartCard, { backgroundColor: colors.CARD_BACKGROUND }]}
        elevation={1}
      >
        <View style={styles.chartHeaderContainer}>
          <Text style={[styles.chartTitle, { color: colors.TEXT_PRIMARY }]}>
            {t("report.chart")}
          </Text>

          <View style={styles.chartFilterContainer}>
            <TouchableOpacity style={styles.filterButton}>
              <Text style={{ color: colors.TEXT_PRIMARY }}>
                {t("report.date")}
              </Text>
              <MaterialCommunityIcons
                name="chevron-down"
                size={20}
                color={colors.TEXT_PRIMARY}
              />
            </TouchableOpacity>

            <TouchableOpacity style={[styles.filterButton, { marginLeft: 8 }]}>
              <MaterialCommunityIcons
                name="chart-pie"
                size={20}
                color={colors.TEXT_PRIMARY}
              />
              <MaterialCommunityIcons
                name="chevron-down"
                size={20}
                color={colors.TEXT_PRIMARY}
              />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.pieChartContainer}>
          <View style={styles.pieChartCenterText}>
            <Text
              style={[
                styles.pieChartCenterLabel,
                { color: colors.TEXT_SECONDARY },
              ]}
            >
              {t("report.totalExpenses")}
            </Text>
            <Text
              style={[
                styles.pieChartCenterValue,
                { color: colors.TEXT_PRIMARY },
              ]}
            >
              25tr
            </Text>
          </View>

          <PieChart
            data={expensesData}
            width={screenWidth - 64}
            height={220}
            chartConfig={chartConfig}
            accessor="population"
            backgroundColor="transparent"
            paddingLeft="85"
            absolute
            hasLegend={false}
          />
        </View>

        <View style={styles.expenseTableContainer}>
          <View style={styles.expenseTableHeader}>
            <Text
              style={[
                styles.expenseTableHeaderText,
                { color: colors.TEXT_SECONDARY, flex: 2 },
              ]}
            >
              {t("report.category")}
            </Text>
            <Text
              style={[
                styles.expenseTableHeaderText,
                { color: colors.TEXT_SECONDARY, flex: 1, textAlign: "right" },
              ]}
            >
              {t("report.expenses")}
            </Text>
            <Text
              style={[
                styles.expenseTableHeaderText,
                { color: colors.TEXT_SECONDARY, flex: 1, textAlign: "right" },
              ]}
            >
              %
            </Text>
          </View>

          <View style={styles.expenseTableRow}>
            <View style={styles.expenseCategoryContainer}>
              <View
                style={[
                  styles.expenseCategoryDot,
                  { backgroundColor: "#4285F4" },
                ]}
              />
              <Text
                style={[
                  styles.expenseCategoryText,
                  { color: colors.TEXT_PRIMARY, flex: 2 },
                ]}
              >
                {t("report.staffSalary")}
              </Text>
            </View>
            <Text
              style={[
                styles.expenseValueText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              12
            </Text>
            <Text
              style={[
                styles.expensePercentText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              62.5%
            </Text>
          </View>

          <View style={styles.expenseTableRow}>
            <View style={styles.expenseCategoryContainer}>
              <View
                style={[
                  styles.expenseCategoryDot,
                  { backgroundColor: "#F4B400" },
                ]}
              />
              <Text
                style={[
                  styles.expenseCategoryText,
                  { color: colors.TEXT_PRIMARY, flex: 2 },
                ]}
              >
                {t("report.maintenance")}
              </Text>
            </View>
            <Text
              style={[
                styles.expenseValueText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              7
            </Text>
            <Text
              style={[
                styles.expensePercentText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              25%
            </Text>
          </View>

          <View style={styles.expenseTableRow}>
            <View style={styles.expenseCategoryContainer}>
              <View
                style={[
                  styles.expenseCategoryDot,
                  { backgroundColor: "#0F9D58" },
                ]}
              />
              <Text
                style={[
                  styles.expenseCategoryText,
                  { color: colors.TEXT_PRIMARY, flex: 2 },
                ]}
              >
                {t("report.renovation")}
              </Text>
            </View>
            <Text
              style={[
                styles.expenseValueText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              6
            </Text>
            <Text
              style={[
                styles.expensePercentText,
                { color: colors.TEXT_PRIMARY, flex: 1, textAlign: "right" },
              ]}
            >
              12.5%
            </Text>
          </View>
        </View>
      </Surface>
    </ScrollView>
  );

  const renderFinanceTab = () => {
    return (
      <View style={styles.tabContent}>
        {renderFinanceSubTabs()}
        {financeSubTab === "revenue" ? renderRevenueTab() : renderExpensesTab()}
      </View>
    );
  };

  const renderOperationsTab = () => {
    return (
      <View style={styles.tabContent}>
        {renderOperationsSubTabs()}
        {operationsSubTab === "room"
          ? renderRoomManagement()
          : renderBuildingTab()}
      </View>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.BACKGROUND }]}>
      <StatusBar
        backgroundColor="#003366"
        barStyle={isDarkMode ? "light-content" : "dark-content"}
      />
      {renderHeader()}
      {activeTab === "operations" ? renderOperationsTab() : renderFinanceTab()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: "#003366",
    paddingTop: 40,
    paddingBottom: 0,
    paddingHorizontal: 16,
  },
  backButton: {
    position: "absolute",
    top: 40,
    left: 16,
    zIndex: 10,
  },
  headerTitle: {
    color: "#FFFFFF",
    fontSize: 20,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 16,
  },
  tabContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 0,
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTabButton: {
    borderBottomColor: "#FFFFFF",
  },
  tabText: {
    color: "#FFFFFF",
    opacity: 0.7,
    fontSize: 16,
  },
  activeTabText: {
    color: "#FFFFFF",
    opacity: 1,
    fontWeight: "bold",
  },
  tabContent: {
    flex: 1,
  },
  subTabContainer: {
    flexDirection: "row",
    backgroundColor: "#003366",
    paddingHorizontal: 16,
  },
  subTabButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    position: "relative",
  },
  activeSubTabButton: {},
  subTabText: {
    color: "#FFFFFF",
    opacity: 0.7,
    fontSize: 16,
  },
  activeSubTabText: {
    color: "#FFFFFF",
    opacity: 1,
    fontWeight: "bold",
  },
  subTabIndicator: {
    position: "absolute",
    bottom: 0,
    left: 24,
    right: 24,
    height: 3,
    backgroundColor: "#FFFFFF",
    borderTopLeftRadius: 3,
    borderTopRightRadius: 3,
  },
  dateRangeCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  datePickerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#F5F5F5",
    padding: 8,
    borderRadius: 8,
    marginTop: 8,
    width: 100,
  },
  sectionCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitleContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  statusFiltersContainer: {
    marginBottom: 16,
  },
  statusFilters: {
    flexDirection: "row",
    paddingVertical: 8,
  },
  statusFilter: {
    marginRight: 8,
  },
  roomCountContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  roomCount: {
    fontSize: 24,
    fontWeight: "bold",
    marginLeft: 8,
  },
  roomCountLabel: {
    marginLeft: 4,
  },
  truyVanButton: {
    marginLeft: "auto",
    backgroundColor: "#003366",
  },
  truyVanButtonText: {
    color: "#FFFFFF",
    fontSize: 12,
  },
  floorContainer: {
    marginBottom: 16,
  },
  floorTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  roomGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  roomItem: {
    width: 70,
    height: 70,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 8,
  },
  roomNumber: {
    color: "#FFFFFF",
    fontWeight: "bold",
  },
  tenantListLabel: {
    fontSize: 12,
    marginBottom: 16,
  },
  tenantList: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 8,
  },
  tenantItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  tenantRoom: {
    fontWeight: "500",
  },
  tenantStatus: {
    fontWeight: "bold",
  },
  divider: {
    height: 1,
    backgroundColor: "#E0E0E0",
  },
  overviewCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  overviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 24,
  },
  statItem: {
    alignItems: "center",
  },
  statValue: {
    fontSize: 24,
    fontWeight: "bold",
  },
  statLabel: {
    fontSize: 12,
  },
  chartTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  chartFilterContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-end",
    marginBottom: 16,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  legendContainer: {
    marginTop: 16,
  },
  legendItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  maintenanceCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  maintenanceList: {
    backgroundColor: "#F5F5F5",
    borderRadius: 8,
    padding: 8,
  },
  maintenanceItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
  },
  maintenanceRoom: {
    fontWeight: "500",
  },
  maintenanceStatus: {
    fontWeight: "bold",
  },
  revenueCard: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  revenueHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  revenueHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  revenueTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  revenueAmount: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  revenuePeriod: {
    fontSize: 12,
  },
  percentageContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: 8,
  },
  percentageBadge: {
    flexDirection: "row",
    alignItems: "center",
  },
  revenueGridContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    margin: 16,
    marginTop: 0,
    gap: 8,
  },
  revenueGridItem: {
    width: "48%",
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
  },
  revenueGridTitle: {
    color: "#FFFFFF",
    fontSize: 14,
    marginBottom: 8,
  },
  revenueGridValue: {
    color: "#FFFFFF",
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
  chartCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  expenseCard: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  expenseHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  expenseHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
  },
  expenseTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginLeft: 8,
  },
  expenseAmount: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  expensePeriod: {
    fontSize: 12,
  },
  chartHeaderContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  filterButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  pieChartContainer: {
    position: "relative",
    alignItems: "center",
    marginVertical: 20,
  },
  pieChartCenterText: {
    position: "absolute",
    top: "50%",
    left: "50%",
    width: 100,
    height: 100,
    marginLeft: -50,
    marginTop: -50,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10,
  },
  pieChartCenterLabel: {
    fontSize: 12,
    textAlign: "center",
  },
  pieChartCenterValue: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
  },
  expenseTableContainer: {
    marginTop: 20,
  },
  expenseTableHeader: {
    flexDirection: "row",
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: "#E0E0E0",
    marginBottom: 12,
  },
  expenseTableHeaderText: {
    fontSize: 14,
  },
  expenseTableRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  expenseCategoryContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 2,
  },
  expenseCategoryDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  expenseCategoryText: {
    fontSize: 14,
  },
  expenseValueText: {
    fontSize: 14,
  },
  expensePercentText: {
    fontSize: 14,
  },
});

export default ReportScreen;
