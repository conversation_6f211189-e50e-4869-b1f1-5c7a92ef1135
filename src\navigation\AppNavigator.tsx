import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import BottomTabNavigator from "./BottomTabNavigator";
import {
  AddRoomScreen,
  RoomDetails,
  MotelsManagement,
  AddEditMotel,
  UpdatePersonalInfo,
} from "../screens";
import { AppStackParamList } from "../types";

const AppStack = createStackNavigator<AppStackParamList>();

const AppNavigator = () => {
  return (
    <AppStack.Navigator
      screenOptions={{
        headerShown: false,
      }}
    >
      <AppStack.Screen name="MainTabs" component={BottomTabNavigator} />
      <AppStack.Screen name="AddRoom" component={AddRoomScreen} />
      <AppStack.Screen name="RoomDetails" component={RoomDetails} />
      <AppStack.Screen name="MotelsManagement" component={MotelsManagement} />
      <AppStack.Screen name="AddEditMotel" component={AddEditMotel} />
      <AppStack.Screen
        name="UpdatePersonalInfo"
        component={UpdatePersonalInfo}
      />
    </AppStack.Navigator>
  );
};

export default AppNavigator;
