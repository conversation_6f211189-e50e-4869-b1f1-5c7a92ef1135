import React, { useEffect } from "react";
import {
  NavigationContainer,
  DefaultTheme,
  DarkTheme,
} from "@react-navigation/native";
import { Provider as PaperProvider } from "react-native-paper";
import { RootNavigator } from "./src/navigation";
import { createPaperTheme } from "./src/theme";
import { LanguageProvider } from "./src/context/LanguageContext";
import { ThemeProvider, useTheme } from "./src/context/ThemeContext";
import { StatusBar } from "react-native";
import { configureGoogleSignIn } from "./src/utils/googleAuthExpo";

// AppContent component to access theme context
const AppContent = () => {
  const { isDarkMode } = useTheme();

  // Create dynamic theme based on current mode
  const paperTheme = createPaperTheme(isDarkMode);
  const navigationTheme = isDarkMode ? DarkTheme : DefaultTheme;

  return (
    <>
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"}
        backgroundColor={isDarkMode ? "#121212" : "#FFFFFF"}
      />
      <PaperProvider theme={paperTheme}>
        <NavigationContainer theme={navigationTheme}>
          <RootNavigator />
        </NavigationContainer>
      </PaperProvider>
    </>
  );
};

export default function App() {
  useEffect(() => {
    configureGoogleSignIn();
    console.log("App initialized");
  }, []);

  return (
    <LanguageProvider>
      <ThemeProvider>
        <AppContent />
      </ThemeProvider>
    </LanguageProvider>
  );
}
