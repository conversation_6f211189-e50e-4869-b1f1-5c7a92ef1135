{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,465,543,619,703,795,878,979,1098,1175,1238,1329,1398,1465,1565,1628,1693,1754,1822,1884,1942,2056,2116,2177,2234,2307,2430,2511,2591,2739,2820,2901,2990,3043,3097,3163,3241,3321,3405,3477,3551,3624,3694,3785,3856,3946,4041,4115,4198,4291,4340,4409,4495,4580,4642,4706,4769,4878,4970,5067,5160,5217,5275", "endLines": "9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "460,538,614,698,790,873,974,1093,1170,1233,1324,1393,1460,1560,1623,1688,1749,1817,1879,1937,2051,2111,2172,2229,2302,2425,2506,2586,2734,2815,2896,2985,3038,3092,3158,3236,3316,3400,3472,3546,3619,3689,3780,3851,3941,4036,4110,4193,4286,4335,4404,4490,4575,4637,4701,4764,4873,4965,5062,5155,5212,5270,5350"}, "to": {"startLines": "2,38,39,40,41,42,52,53,54,57,58,63,66,68,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3238,3316,3392,3476,3568,4557,4658,4777,5036,5099,5570,5785,5919,6019,6082,6147,6208,6276,6338,6396,6510,6570,6631,6688,6761,7111,7192,7272,7420,7501,7582,7671,7724,7778,7844,7922,8002,8086,8158,8232,8305,8375,8466,8537,8627,8722,8796,8879,8972,9021,9090,9176,9261,9323,9387,9450,9559,9651,9748,9841,9898,9956", "endLines": "9,38,39,40,41,42,52,53,54,57,58,63,66,68,69,70,71,72,73,74,75,76,77,78,79,80,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120", "endColumns": "12,77,75,83,91,82,100,118,76,62,90,68,66,99,62,64,60,67,61,57,113,59,60,56,72,122,80,79,147,80,80,88,52,53,65,77,79,83,71,73,72,69,90,70,89,94,73,82,92,48,68,85,84,61,63,62,108,91,96,92,56,57,79", "endOffsets": "510,3311,3387,3471,3563,3646,4653,4772,4849,5094,5185,5634,5847,6014,6077,6142,6203,6271,6333,6391,6505,6565,6626,6683,6756,6879,7187,7267,7415,7496,7577,7666,7719,7773,7839,7917,7997,8081,8153,8227,8300,8370,8461,8532,8622,8717,8791,8874,8967,9016,9085,9171,9256,9318,9382,9445,9554,9646,9743,9836,9893,9951,10031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "43,44,45,46,47,48,49,134", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3651,3744,3846,3941,4044,4147,4249,11068", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "3739,3841,3936,4039,4142,4244,4358,11164"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,233,318,400,472,540,618,685,755,833,912,993,1081,1159,1239,1323,1397,1475,1552,1627,1706,1778,1862,1932,2018,2087", "endColumns": "68,108,84,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "119,228,313,395,467,535,613,680,750,828,907,988,1076,1154,1234,1318,1392,1470,1547,1622,1701,1773,1857,1927,2013,2082,2160"}, "to": {"startLines": "37,50,51,55,62,64,65,67,81,82,83,121,122,123,124,126,127,128,129,130,131,132,133,135,136,137,138", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3169,4363,4472,4854,5498,5639,5707,5852,6884,6954,7032,10036,10117,10205,10283,10445,10529,10603,10681,10758,10833,10912,10984,11169,11239,11325,11394", "endColumns": "68,108,84,81,71,67,77,66,69,77,78,80,87,77,79,83,73,77,76,74,78,71,83,69,85,68,77", "endOffsets": "3233,4467,4552,4931,5565,5702,5780,5914,6949,7027,7106,10112,10200,10278,10358,10524,10598,10676,10753,10828,10907,10979,11063,11234,11320,11389,11467"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "56,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "4936,5190,5288,5396", "endColumns": "99,97,107,101", "endOffsets": "5031,5283,5391,5493"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,317,424,506,607,721,801,880,971,1064,1156,1250,1350,1443,1538,1631,1722,1816,1895,2000,2098,2196,2304,2404,2507,2662,2759", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "208,312,419,501,602,716,796,875,966,1059,1151,1245,1345,1438,1533,1626,1717,1811,1890,1995,2093,2191,2299,2399,2502,2657,2754,2836"}, "to": {"startLines": "10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "515,623,727,834,916,1017,1131,1211,1290,1381,1474,1566,1660,1760,1853,1948,2041,2132,2226,2305,2410,2508,2606,2714,2814,2917,3072,10363", "endColumns": "107,103,106,81,100,113,79,78,90,92,91,93,99,92,94,92,90,93,78,104,97,97,107,99,102,154,96,81", "endOffsets": "618,722,829,911,1012,1126,1206,1285,1376,1469,1561,1655,1755,1848,1943,2036,2127,2221,2300,2405,2503,2601,2709,2809,2912,3067,3164,10440"}}]}]}