# Theo <PERSON>õi Tiến Độ
## Nhiệm Vụ Ưu Tiên
### Phát Triển Backend
- [ ] M<PERSON><PERSON> hình RoomDetails
- [ ] M<PERSON>n hinh Report
- [ ] Tính năng Quản lý <PERSON> sơ
  - [ ] Chức năng đổi mật khẩu
  - [ ] Chuyển đổi giữa các tòa nhà/nhà trọ


### Phát Triển Frontend
- [ ] Màn hình CRUD RoomDetails
  - [ ] Thông tin phòng
  - [ ] Người thuê
  - [ ] Hóa đơn
- [ ] Giao diện Quản lý Tòa nhà (Sau khi hoàn thành cái CURD RoomDetails)
  - [ ] Thao tác CRUD cho tòa nhà/nhà trọ



## Tính Năng Tương Lai
Các tính năng sau sẽ được triển khai sau khi hoàn thành các nhiệm vụ ưu tiên ở trên:
- [ ] Triể<PERSON> khai <PERSON>

- [ ] Nhập Dữ liệu Thông minh
  - <PERSON>ứ<PERSON> năng nhập file CSV
  - **<PERSON><PERSON> thể** tích hợp Google Form.

- [ ] Xem log cho owner

## Vấn đề cần khắc phục
- Check lại data chuyển từ HomeScreen sang RoomDetails
- Chuyển config/firebase.ts vào .env trước khi build file apk để publish
