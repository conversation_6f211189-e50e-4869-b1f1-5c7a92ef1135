import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as WebBrowser from "expo-web-browser";
import { getGoogleClientId, getOAuthScopes } from "../config/oauth";

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({
    useProxy: true,
  });

  const developmentRedirectUri = AuthSession.makeRedirectUri({
    useProxy: true,
  });

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", getGoogleClientId());
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle = async (): Promise<GoogleSignInResult> => {
  try {
    // Real Google OAuth implementation using Expo AuthSession
    console.log("Starting Google OAuth flow...");

    // Use Expo proxy redirect URI for OAuth compatibility
    const redirectUri = "https://auth.expo.io/@sirtterr/client";

    console.log("Redirect URI:", redirectUri);
    console.log("Client ID:", getGoogleClientId());
    console.log("Scopes:", getOAuthScopes());

    // Use manual WebBrowser approach to avoid AuthSession PKCE issues
    const authUrl =
      `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${encodeURIComponent(getGoogleClientId())}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `response_type=id_token&` +
      `scope=${encodeURIComponent(getOAuthScopes().join(" "))}&` +
      `nonce=${Math.random().toString(36).substring(2, 15)}`;

    console.log("Manual auth URL:", authUrl.substring(0, 100) + "...");

    const result = await WebBrowser.openAuthSessionAsync(authUrl, redirectUri);

    console.log("WebBrowser OAuth configuration:", {
      clientId: getGoogleClientId(),
      scopes: getOAuthScopes(),
      redirectUri,
      responseType: "id_token",
      manual_url: true,
    });

    console.log("OAuth result:", result);
    console.log("OAuth result type:", result.type);
    console.log("OAuth result full object:", JSON.stringify(result, null, 2));

    if (result.type === "success") {
      console.log("OAuth success! Parsing URL for tokens...");
      console.log("Result URL:", result.url);

      if (result.url) {
        // Parse the URL to extract tokens from fragment
        let idToken = null;

        try {
          // Try URL fragment first (standard for implicit flow)
          const urlParts = result.url.split("#");
          if (urlParts.length > 1) {
            const fragment = urlParts[1];
            console.log("URL fragment:", fragment);
            const params = new URLSearchParams(fragment);
            idToken = params.get("id_token");
            console.log("Extracted ID token from URL fragment:", !!idToken);

            // Log all fragment parameters for debugging
            console.log("All fragment parameters:");
            for (const [key, value] of params.entries()) {
              console.log(`  ${key}: ${value.substring(0, 20)}...`);
            }
          }

          // Try query parameters as fallback
          if (!idToken) {
            const url = new URL(result.url);
            idToken = url.searchParams.get("id_token");
            console.log("Extracted ID token from query params:", !!idToken);

            // Log all query parameters for debugging
            console.log("All query parameters:");
            for (const [key, value] of url.searchParams.entries()) {
              console.log(`  ${key}: ${value.substring(0, 20)}...`);
            }
          }

          if (idToken) {
            console.log("ID token found! Creating Firebase credential...");

            const googleCredential = GoogleAuthProvider.credential(idToken);

            const userCredential = await signInWithCredential(
              auth,
              googleCredential
            );

            console.log(
              "Firebase sign-in successful:",
              userCredential.user.email
            );

            return {
              user: userCredential.user,
              additionalUserInfo: {
                isNewUser:
                  userCredential.user.metadata.creationTime ===
                  userCredential.user.metadata.lastSignInTime,
                profile: {
                  email: userCredential.user.email,
                  name: userCredential.user.displayName,
                  picture: userCredential.user.photoURL,
                },
              },
            };
          } else {
            console.error("No ID token found in URL");
            throw new Error("No ID token found in OAuth response URL");
          }
        } catch (parseError: any) {
          console.error("URL parsing error:", parseError);
          throw new Error(
            `Failed to parse OAuth response: ${parseError.message}`
          );
        }
      } else {
        console.error("No URL in success response");
        throw new Error("No URL returned in OAuth success response");
      }
    } else if (result.type === "cancel" || result.type === "dismiss") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      // WebBrowser result types: "success" | "cancel" | "dismiss" | "opened" | "locked"
      const errorMessage = `GOOGLE_SIGN_IN_ERROR - Result type: ${result.type}`;
      throw new Error(errorMessage);
    }
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.message === "SIGN_IN_CANCELLED") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
    }
  }
};

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
