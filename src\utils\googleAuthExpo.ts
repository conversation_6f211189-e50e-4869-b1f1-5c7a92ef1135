import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";

// Google OAuth Configuration
const GOOGLE_CLIENT_ID =
  "728339198977-qnipi0l8mj5lcblnq1gm1pd1kupse12p.apps.googleusercontent.com";
const OAUTH_SCOPES = ["openid", "profile", "email"];

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({});
  const developmentRedirectUri = AuthSession.makeRedirectUri({});

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", GOOGLE_CLIENT_ID);
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle = async (): Promise<GoogleSignInResult> => {
  try {
    console.log(
      "🚧 DEVELOPMENT NOTE: Google OAuth with Expo Go has limitations"
    );
    console.log("📝 For production, consider:");
    console.log(
      "   1. Expo Development Build + @react-native-google-signin/google-signin"
    );
    console.log("   2. Custom backend OAuth implementation");
    console.log("   3. Firebase Auth with custom domain");

    console.log(
      "⚠️  Current implementation will show 'dismiss' due to auth.expo.io limitations"
    );
    console.log("🔄 Attempting OAuth flow anyway...");

    // Use the standard AuthSession approach
    const redirectUri = AuthSession.makeRedirectUri({});

    console.log("Redirect URI:", redirectUri);
    console.log("Client ID:", GOOGLE_CLIENT_ID);

    const request = new AuthSession.AuthRequest({
      clientId: GOOGLE_CLIENT_ID,
      scopes: OAUTH_SCOPES,
      redirectUri,
      responseType: AuthSession.ResponseType.Code,
      extraParams: {},
    });

    const discovery = {
      authorizationEndpoint: "https://accounts.google.com/o/oauth2/v2/auth",
      tokenEndpoint: "https://oauth2.googleapis.com/token",
    };

    const result = await request.promptAsync(discovery);

    console.log("OAuth result:", result);
    console.log("OAuth result type:", result.type);

    if (result.type === "success" && result.params?.code) {
      console.log("✅ OAuth success! This is unexpected but great!");

      try {
        const tokenResult = await AuthSession.exchangeCodeAsync(
          {
            clientId: GOOGLE_CLIENT_ID,
            code: result.params.code,
            redirectUri,
            extraParams: {},
          },
          discovery
        );

        if (tokenResult.idToken) {
          const googleCredential = GoogleAuthProvider.credential(
            tokenResult.idToken,
            tokenResult.accessToken
          );

          const userCredential = await signInWithCredential(
            auth,
            googleCredential
          );

          return {
            user: userCredential.user,
            additionalUserInfo: {
              isNewUser:
                userCredential.user.metadata.creationTime ===
                userCredential.user.metadata.lastSignInTime,
              profile: {
                email: userCredential.user.email,
                name: userCredential.user.displayName,
                picture: userCredential.user.photoURL,
              },
            },
          };
        }
      } catch (exchangeError: any) {
        console.error("Token exchange failed:", exchangeError);
        throw new Error(`Token exchange failed: ${exchangeError.message}`);
      }
    }

    // If we get here, OAuth failed (expected with Expo Go)
    console.log("❌ OAuth failed as expected with Expo Go");
    console.log("💡 This is a known limitation - see logs above for solutions");

    if (result.type === "dismiss" || result.type === "cancel") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      throw new Error(`GOOGLE_SIGN_IN_ERROR - Result type: ${result.type}`);
    }
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.message === "SIGN_IN_CANCELLED") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
    }
  }
};

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
