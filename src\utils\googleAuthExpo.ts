import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";

// Google OAuth Configuration
const GOOGLE_CLIENT_ID =
  "728339198977-qnipi0l8mj5lcblnq1gm1pd1kupse12p.apps.googleusercontent.com";
const OAUTH_SCOPES = ["openid", "profile", "email"];

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({});
  const developmentRedirectUri = AuthSession.makeRedirectUri({});

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", GOOGLE_CLIENT_ID);
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle = async (): Promise<GoogleSignInResult> => {
  try {
    console.log("Starting Google OAuth flow with WebBrowser...");

    // Use a custom redirect URI that works with WebBrowser
    const redirectUri = AuthSession.makeRedirectUri({
      scheme: "com.datacoolie.quanlynhatro",
      path: "oauth",
    });

    console.log("Redirect URI:", redirectUri);
    console.log("Client ID:", GOOGLE_CLIENT_ID);
    console.log("Scopes:", OAUTH_SCOPES);

    // Create OAuth URL manually for better control
    const authUrl =
      `https://accounts.google.com/o/oauth2/v2/auth?` +
      `client_id=${encodeURIComponent(GOOGLE_CLIENT_ID)}&` +
      `redirect_uri=${encodeURIComponent(redirectUri)}&` +
      `response_type=id_token&` +
      `scope=${encodeURIComponent(OAUTH_SCOPES.join(" "))}&` +
      `nonce=${Math.random().toString(36).substring(2, 15)}`;

    console.log("Auth URL:", authUrl.substring(0, 100) + "...");

    // Use WebBrowser for OAuth
    const result = await WebBrowser.openAuthSessionAsync(authUrl, redirectUri);

    console.log("OAuth result:", result);
    console.log("OAuth result type:", result.type);

    if (result.type === "success" && result.url) {
      console.log("OAuth success! Parsing URL for tokens...");
      console.log("Result URL:", result.url);

      try {
        // Parse the URL to extract the ID token from fragment
        let idToken = null;

        // Try URL fragment first (standard for implicit flow)
        const urlParts = result.url.split("#");
        if (urlParts.length > 1) {
          const fragment = urlParts[1];
          console.log("URL fragment:", fragment);
          const params = new URLSearchParams(fragment);
          idToken = params.get("id_token");
          console.log("Extracted ID token from URL fragment:", !!idToken);

          // Log all fragment parameters for debugging
          console.log("All fragment parameters:");
          for (const [key, value] of params.entries()) {
            console.log(`  ${key}: ${value.substring(0, 20)}...`);
          }
        }

        // Try query parameters as fallback
        if (!idToken) {
          const url = new URL(result.url);
          idToken = url.searchParams.get("id_token");
          console.log("Extracted ID token from query params:", !!idToken);

          // Log all query parameters for debugging
          console.log("All query parameters:");
          for (const [key, value] of url.searchParams.entries()) {
            console.log(`  ${key}: ${value.substring(0, 20)}...`);
          }
        }

        if (idToken) {
          console.log("ID token found! Creating Firebase credential...");

          const googleCredential = GoogleAuthProvider.credential(idToken);

          const userCredential = await signInWithCredential(
            auth,
            googleCredential
          );

          console.log(
            "Firebase sign-in successful:",
            userCredential.user.email
          );

          return {
            user: userCredential.user,
            additionalUserInfo: {
              isNewUser:
                userCredential.user.metadata.creationTime ===
                userCredential.user.metadata.lastSignInTime,
              profile: {
                email: userCredential.user.email,
                name: userCredential.user.displayName,
                picture: userCredential.user.photoURL,
              },
            },
          };
        } else {
          console.error("No ID token found in URL");
          throw new Error("No ID token found in OAuth response URL");
        }
      } catch (parseError: any) {
        console.error("URL parsing error:", parseError);
        throw new Error(
          `Failed to parse OAuth response: ${parseError.message}`
        );
      }
    } else if (result.type === "cancel" || result.type === "dismiss") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      console.error("OAuth failed or no URL returned");
      console.log("Full result object:", JSON.stringify(result, null, 2));
      const errorMessage = `GOOGLE_SIGN_IN_ERROR - Result type: ${result.type}`;
      throw new Error(errorMessage);
    }
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.message === "SIGN_IN_CANCELLED") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
    }
  }
};

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
