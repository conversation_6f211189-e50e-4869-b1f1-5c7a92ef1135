import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { getGoogleClientId, getOAuthScopes } from "../config/oauth";

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({});
  const developmentRedirectUri = AuthSession.makeRedirectUri({});

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", getGoogleClientId());
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle = async (): Promise<GoogleSignInResult> => {
  try {
    // Real Google OAuth implementation using Expo AuthSession
    console.log("Starting Google OAuth flow...");

    // Use Expo proxy redirect URI for OAuth compatibility
    const redirectUri = "https://auth.expo.io/@sirtterr/client";

    console.log("Redirect URI:", redirectUri);
    console.log("Client ID:", getGoogleClientId());
    console.log("Scopes:", getOAuthScopes());

    // Use AuthSession with PKCE enabled (the proper way)
    const request = new AuthSession.AuthRequest({
      clientId: getGoogleClientId(),
      scopes: getOAuthScopes(),
      redirectUri,
      responseType: AuthSession.ResponseType.Code,
      extraParams: {},
      // Let PKCE be enabled by default - this is the secure way
    });

    const discovery = {
      authorizationEndpoint: "https://accounts.google.com/o/oauth2/v2/auth",
      tokenEndpoint: "https://oauth2.googleapis.com/token",
    };

    console.log("Using AuthSession with PKCE enabled");
    const result = await request.promptAsync(discovery);

    console.log("AuthSession OAuth configuration:", {
      clientId: getGoogleClientId(),
      scopes: getOAuthScopes(),
      redirectUri,
      responseType: "code",
      pkce_enabled: true,
    });

    console.log("OAuth result:", result);
    console.log("OAuth result type:", result.type);
    console.log("OAuth result full object:", JSON.stringify(result, null, 2));

    if (result.type === "success") {
      console.log("OAuth success! Checking for authorization code...");
      console.log("Available params:", Object.keys(result.params || {}));

      if (result.params?.code) {
        console.log("Authorization code received, exchanging for tokens...");

        try {
          // Use AuthSession's built-in token exchange with PKCE
          const tokenResult = await AuthSession.exchangeCodeAsync(
            {
              clientId: getGoogleClientId(),
              code: result.params.code,
              redirectUri,
              extraParams: {},
            },
            discovery
          );

          console.log("Token exchange result:", {
            hasAccessToken: !!tokenResult.accessToken,
            hasIdToken: !!tokenResult.idToken,
            hasRefreshToken: !!tokenResult.refreshToken,
          });

          if (tokenResult.idToken) {
            console.log("ID token received from token exchange!");

            const googleCredential = GoogleAuthProvider.credential(
              tokenResult.idToken,
              tokenResult.accessToken
            );

            const userCredential = await signInWithCredential(
              auth,
              googleCredential
            );

            console.log(
              "Firebase sign-in successful:",
              userCredential.user.email
            );

            return {
              user: userCredential.user,
              additionalUserInfo: {
                isNewUser:
                  userCredential.user.metadata.creationTime ===
                  userCredential.user.metadata.lastSignInTime,
                profile: {
                  email: userCredential.user.email,
                  name: userCredential.user.displayName,
                  picture: userCredential.user.photoURL,
                },
              },
            };
          } else {
            console.error("No ID token in token exchange result");
            throw new Error("No ID token received from token exchange");
          }
        } catch (exchangeError: any) {
          console.error("Token exchange failed:", exchangeError);
          throw new Error(`Token exchange failed: ${exchangeError.message}`);
        }
      } else {
        console.error("No authorization code found in OAuth response");
        console.log("Full result object:", JSON.stringify(result, null, 2));
        console.log("Available params:", Object.keys(result.params || {}));

        throw new Error("No authorization code received from Google OAuth");
      }
    } else if (result.type === "cancel" || result.type === "dismiss") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      // AuthSession result types: "success" | "cancel" | "dismiss" | "error" | "opened" | "locked"
      const errorMessage = `GOOGLE_SIGN_IN_ERROR - Result type: ${result.type}`;
      throw new Error(errorMessage);
    }
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.message === "SIGN_IN_CANCELLED") {
      throw new Error("SIGN_IN_CANCELLED");
    } else {
      throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
    }
  }
};

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
