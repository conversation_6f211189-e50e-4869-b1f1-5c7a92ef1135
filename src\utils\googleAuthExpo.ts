import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { Platform, Alert } from "react-native";

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

// mock google client id
const GOOGLE_CLIENT_ID = "728339198977-placeholder.apps.googleusercontent.com";

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle =
  async (): Promise<GoogleSignInResult | null> => {
    try {
      if (__DEV__) {
        return new Promise((resolve, reject) => {
          Alert.alert(
            "Google Sign-In (Development)",
            "Google Sign-In is configured but requires a development build to work properly. In Expo Go, this is a placeholder.\n\nTo test Google Sign-In:\n1. Create a development build\n2. Configure proper Google OAuth credentials\n3. Test on a real device",
            [
              {
                text: "Cancel",
                style: "cancel",
                onPress: () => reject(new Error("SIGN_IN_CANCELLED")),
              },
              {
                text: "Simulate Success",
                onPress: () => {
                  // Simulate a successful sign-in for development
                  const mockUser = {
                    user: {
                      uid: "mock-google-user-id",
                      email: "<EMAIL>",
                      displayName: "Test User",
                      photoURL: "https://via.placeholder.com/150",
                      metadata: {
                        creationTime: new Date().toISOString(),
                        lastSignInTime: new Date().toISOString(),
                      },
                    },
                    additionalUserInfo: {
                      isNewUser: true,
                      profile: {
                        email: "<EMAIL>",
                        name: "Test User",
                        picture: "https://via.placeholder.com/150",
                      },
                    },
                  };
                  resolve(mockUser);
                },
              },
            ]
          );
        });
      }

      const redirectUri = AuthSession.makeRedirectUri({
        useProxy: true,
      });

      const request = new AuthSession.AuthRequest({
        clientId: GOOGLE_CLIENT_ID,
        scopes: ["openid", "profile", "email"],
        redirectUri,
        responseType: AuthSession.ResponseType.IdToken,
        extraParams: {},
      });

      const result = await request.promptAsync({
        authorizationEndpoint: "https://accounts.google.com/oauth/authorize",
      });

      if (result.type === "success" && result.params.id_token) {
        const googleCredential = GoogleAuthProvider.credential(
          result.params.id_token
        );

        const userCredential = await signInWithCredential(
          auth,
          googleCredential
        );

        return {
          user: userCredential.user,
          additionalUserInfo: {
            isNewUser:
              userCredential.user.metadata.creationTime ===
              userCredential.user.metadata.lastSignInTime,
            profile: {
              email: userCredential.user.email,
              name: userCredential.user.displayName,
              picture: userCredential.user.photoURL,
            },
          },
        };
      } else if (result.type === "cancel") {
        throw new Error("SIGN_IN_CANCELLED");
      } else {
        throw new Error("GOOGLE_SIGN_IN_ERROR");
      }
    } catch (error: any) {
      console.error("Google Sign-In Error:", error);

      if (error.message === "SIGN_IN_CANCELLED") {
        throw new Error("SIGN_IN_CANCELLED");
      } else {
        throw new Error("GOOGLE_SIGN_IN_ERROR");
      }
    }
  };

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
