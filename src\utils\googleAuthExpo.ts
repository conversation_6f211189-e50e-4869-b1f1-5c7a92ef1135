import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import { getGoogleClientId } from "../config/oauth";
import {
  GoogleSignin,
  statusCodes,
} from "@react-native-google-signin/google-signin";

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({});
  const developmentRedirectUri = AuthSession.makeRedirectUri({});

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", getGoogleClientId());
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Configuring Google Sign-In with library...");

  GoogleSignin.configure({
    webClientId: getGoogleClientId(),
    offlineAccess: true,
    hostedDomain: "",
    forceCodeForRefreshToken: true,
  });

  console.log("Google Sign-In configured with client ID:", getGoogleClientId());
};

export const signInWithGoogle = async (): Promise<GoogleSignInResult> => {
  try {
    console.log("Starting Google Sign-In with library...");

    // Check if Google Play Services are available
    await GoogleSignin.hasPlayServices();
    console.log("Google Play Services available");

    // Sign in with Google
    const userInfo = await GoogleSignin.signIn();
    console.log("Google Sign-In successful:", userInfo.data?.user.email);

    // Get the ID token
    const idToken = userInfo.data?.idToken;
    if (!idToken) {
      throw new Error("No ID token received from Google Sign-In");
    }

    console.log("ID token received, creating Firebase credential...");

    // Create Firebase credential
    const googleCredential = GoogleAuthProvider.credential(idToken);

    // Sign in to Firebase
    const userCredential = await signInWithCredential(auth, googleCredential);

    console.log("Firebase sign-in successful:", userCredential.user.email);

    return {
      user: userCredential.user,
      additionalUserInfo: {
        isNewUser:
          userCredential.user.metadata.creationTime ===
          userCredential.user.metadata.lastSignInTime,
        profile: {
          email: userCredential.user.email,
          name: userCredential.user.displayName,
          picture: userCredential.user.photoURL,
        },
      },
    };
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      throw new Error("SIGN_IN_CANCELLED");
    } else if (error.code === statusCodes.IN_PROGRESS) {
      throw new Error("SIGN_IN_IN_PROGRESS");
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      throw new Error("PLAY_SERVICES_NOT_AVAILABLE");
    } else {
      throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
    }
  }
};

export const signOutFromGoogle = async () => {
  try {
    await GoogleSignin.signOut();
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    const currentUser = GoogleSignin.getCurrentUser();
    return !!currentUser && !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await GoogleSignin.revokeAccess();
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
