import { GoogleAuthProvider, signInWithCredential, User } from "firebase/auth";
import { auth } from "../../config/firebase";
import * as AuthSession from "expo-auth-session";
import * as WebBrowser from "expo-web-browser";
import { getGoogleClientId, getOAuthScopes } from "../config/oauth";

interface GoogleSignInResult {
  user: User | any;
  additionalUserInfo?: {
    isNewUser: boolean;
    profile: {
      email: string | null;
      name: string | null;
      picture: string | null;
    };
  };
}

WebBrowser.maybeCompleteAuthSession();

export const testRedirectUri = () => {
  // For Expo SDK 50, use Expo's auth proxy
  const productionRedirectUri = AuthSession.makeRedirectUri({
    useProxy: true,
  });

  const developmentRedirectUri = AuthSession.makeRedirectUri({
    useProxy: true,
  });

  console.log("=== REDIRECT URIs FOR GOOGLE CLOUD CONSOLE ===");
  console.log("Production Redirect URI:", productionRedirectUri);
  console.log("Development Redirect URI:", developmentRedirectUri);
  console.log("Client ID:", getGoogleClientId());
  console.log("===============================================");

  return {
    production: productionRedirectUri,
    development: developmentRedirectUri,
  };
};

export const configureGoogleSignIn = () => {
  console.log("Google Sign-In configured for Expo");
};

export const signInWithGoogle =
  async (): Promise<GoogleSignInResult | null> => {
    try {
      // Real Google OAuth implementation using Expo AuthSession
      console.log("Starting Google OAuth flow...");

      // Use proper redirect URI configuration
      const redirectUri = AuthSession.makeRedirectUri({
        scheme: __DEV__ ? undefined : "com.motelmanagement.client",
        useProxy: true,
        preferLocalhost: false,
      });

      console.log("Redirect URI:", redirectUri);
      console.log("Client ID:", getGoogleClientId());
      console.log("Scopes:", getOAuthScopes());

      const request = new AuthSession.AuthRequest({
        clientId: getGoogleClientId(),
        scopes: getOAuthScopes(),
        redirectUri,
        responseType: AuthSession.ResponseType.IdToken,
        extraParams: {},
      });

      console.log("Request configuration:", {
        clientId: getGoogleClientId(),
        scopes: getOAuthScopes(),
        redirectUri,
        responseType: AuthSession.ResponseType.IdToken,
      });

      const discovery = {
        authorizationEndpoint: "https://accounts.google.com/o/oauth2/v2/auth",
        tokenEndpoint: "https://oauth2.googleapis.com/token",
        revocationEndpoint: "https://oauth2.googleapis.com/revoke",
      };

      const result = await request.promptAsync(discovery);

      console.log("OAuth result:", result);
      console.log("OAuth result type:", result.type);

      if (result.type === "success") {
        console.log("OAuth result params:", result.params);
      } else if (result.type === "error") {
        console.log("OAuth error details:", result.error);
        console.log("OAuth error params:", result.params);
      }

      if (result.type === "success" && result.params.id_token) {
        const googleCredential = GoogleAuthProvider.credential(
          result.params.id_token
        );

        const userCredential = await signInWithCredential(
          auth,
          googleCredential
        );

        return {
          user: userCredential.user,
          additionalUserInfo: {
            isNewUser:
              userCredential.user.metadata.creationTime ===
              userCredential.user.metadata.lastSignInTime,
            profile: {
              email: userCredential.user.email,
              name: userCredential.user.displayName,
              picture: userCredential.user.photoURL,
            },
          },
        };
      } else if (result.type === "cancel") {
        throw new Error("SIGN_IN_CANCELLED");
      } else {
        const errorMessage =
          result.type === "error"
            ? `OAuth Error: ${result.error?.code || "unknown"} - ${
                result.error?.description || "No description"
              }`
            : "GOOGLE_SIGN_IN_ERROR";
        throw new Error(errorMessage);
      }
    } catch (error: any) {
      console.error("Google Sign-In Error:", error);

      if (error.message === "SIGN_IN_CANCELLED") {
        throw new Error("SIGN_IN_CANCELLED");
      } else {
        throw new Error(error.message || "GOOGLE_SIGN_IN_ERROR");
      }
    }
  };

export const signOutFromGoogle = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return !!auth.currentUser;
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    return auth.currentUser;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
