{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-ko/values-ko.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,296,397,479,577,683,763,838,929,1022,1117,1211,1311,1404,1499,1593,1684,1775,1855,1953,2047,2142,2242,2339,2439,2591,2685", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "197,291,392,474,572,678,758,833,924,1017,1112,1206,1306,1399,1494,1588,1679,1770,1850,1948,2042,2137,2237,2334,2434,2586,2680,2759"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,396,490,591,673,771,877,957,1032,1123,1216,1311,1405,1505,1598,1693,1787,1878,1969,2049,2147,2241,2336,2436,2533,2633,2785,9390", "endColumns": "96,93,100,81,97,105,79,74,90,92,94,93,99,92,94,93,90,90,79,97,93,94,99,96,99,151,93,78", "endOffsets": "391,485,586,668,766,872,952,1027,1118,1211,1306,1400,1500,1593,1688,1782,1873,1964,2044,2142,2236,2331,2431,2528,2628,2780,2874,9464"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,141,234,337", "endColumns": "85,92,102,93", "endOffsets": "136,229,332,426"}, "to": {"startLines": "52,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4490,4719,4812,4915", "endColumns": "85,92,102,93", "endOffsets": "4571,4807,4910,5004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,247,341,438,534,632,732", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "142,242,336,433,529,627,727,828"}, "to": {"startLines": "39,40,41,42,43,44,45,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3296,3388,3488,3582,3679,3775,3873,10062", "endColumns": "91,99,93,96,95,97,99,100", "endOffsets": "3383,3483,3577,3674,3770,3868,3968,10158"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,249,314,378,447,521,600,683,789,864,926,1007,1069,1126,1213,1273,1331,1389,1448,1505,1559,1654,1710,1767,1821,1887,1991,2066,2143,2264,2329,2394,2473,2523,2574,2640,2704,2774,2851,2919,2990,3057,3127,3207,3284,3364,3446,3518,3583,3655,3703,3767,3842,3919,3981,4045,4108,4192,4271,4351,4431,4485,4540", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,78,49,50,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,53,54,71", "endOffsets": "244,309,373,442,516,595,678,784,859,921,1002,1064,1121,1208,1268,1326,1384,1443,1500,1554,1649,1705,1762,1816,1882,1986,2061,2138,2259,2324,2389,2468,2518,2569,2635,2699,2769,2846,2914,2985,3052,3122,3202,3279,3359,3441,3513,3578,3650,3698,3762,3837,3914,3976,4040,4103,4187,4266,4346,4426,4480,4535,4607"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2945,3010,3074,3143,3217,4154,4237,4343,4576,4638,5076,5282,5404,5491,5551,5609,5667,5726,5783,5837,5932,5988,6045,6099,6165,6478,6553,6630,6751,6816,6881,6960,7010,7061,7127,7191,7261,7338,7406,7477,7544,7614,7694,7771,7851,7933,8005,8070,8142,8190,8254,8329,8406,8468,8532,8595,8679,8758,8838,8918,8972,9027", "endLines": "5,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,64,63,68,73,78,82,105,74,61,80,61,56,86,59,57,57,58,56,53,94,55,56,53,65,103,74,76,120,64,64,78,49,50,65,63,69,76,67,70,66,69,79,76,79,81,71,64,71,47,63,74,76,61,63,62,83,78,79,79,53,54,71", "endOffsets": "294,3005,3069,3138,3212,3291,4232,4338,4413,4633,4714,5133,5334,5486,5546,5604,5662,5721,5778,5832,5927,5983,6040,6094,6160,6264,6548,6625,6746,6811,6876,6955,7005,7056,7122,7186,7256,7333,7401,7472,7539,7609,7689,7766,7846,7928,8000,8065,8137,8185,8249,8324,8401,8463,8527,8590,8674,8753,8833,8913,8967,9022,9094"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-ko\\values-ko.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,218,302,374,441,508,585,650,715,787,859,936,1011,1077,1150,1224,1297,1374,1450,1522,1592,1661,1743,1811,1882,1949", "endColumns": "65,96,83,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "116,213,297,369,436,503,580,645,710,782,854,931,1006,1072,1145,1219,1292,1369,1445,1517,1587,1656,1738,1806,1877,1944,2016"}, "to": {"startLines": "33,46,47,51,58,60,61,63,77,78,79,117,118,119,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2879,3973,4070,4418,5009,5138,5205,5339,6269,6334,6406,9099,9176,9251,9317,9469,9543,9616,9693,9769,9841,9911,9980,10163,10231,10302,10369", "endColumns": "65,96,83,71,66,66,76,64,64,71,71,76,74,65,72,73,72,76,75,71,69,68,81,67,70,66,71", "endOffsets": "2940,4065,4149,4485,5071,5200,5277,5399,6329,6401,6473,9171,9246,9312,9385,9538,9611,9688,9764,9836,9906,9975,10057,10226,10297,10364,10436"}}]}]}