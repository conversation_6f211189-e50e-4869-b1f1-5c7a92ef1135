<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\src\main\res"><file name="rn_edit_text_material" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable\rn_edit_text_material.xml" qualifiers="" type="drawable"/><file name="splashscreen" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable\splashscreen.xml" qualifiers="" type="drawable"/><file name="splashscreen_image" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable-hdpi\splashscreen_image.png" qualifiers="hdpi-v4" type="drawable"/><file name="splashscreen_image" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable-mdpi\splashscreen_image.png" qualifiers="mdpi-v4" type="drawable"/><file name="splashscreen_image" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable-xhdpi\splashscreen_image.png" qualifiers="xhdpi-v4" type="drawable"/><file name="splashscreen_image" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable-xxhdpi\splashscreen_image.png" qualifiers="xxhdpi-v4" type="drawable"/><file name="splashscreen_image" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\drawable-xxxhdpi\splashscreen_image.png" qualifiers="xxxhdpi-v4" type="drawable"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-hdpi\ic_launcher_foreground.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-mdpi\ic_launcher_foreground.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xhdpi\ic_launcher_foreground.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_foreground.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_foreground" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_foreground.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Freelance\MotelManagement\client\android\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Freelance\MotelManagement\client\android\app\src\main\res\values\colors.xml" qualifiers=""><color name="splashscreen_background">#ffffff</color><color name="iconBackground">#ffffff</color><color name="colorPrimary">#023c69</color><color name="colorPrimaryDark">#ffffff</color></file><file path="D:\Freelance\MotelManagement\client\android\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">client</string><string name="expo_splash_screen_resize_mode" translatable="false">contain</string><string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string><string name="expo_runtime_version">1.0.0</string></file><file path="D:\Freelance\MotelManagement\client\android\app\src\main\res\values\styles.xml" qualifiers=""><style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
  </style><style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style><style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/splashscreen</item>
  </style></file><file path="D:\Freelance\MotelManagement\client\android\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\resValues\debug"/><source path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\google-services\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\resValues\debug"><file path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\resValues\debug\values\gradleResValues.xml" qualifiers=""><integer name="react_native_dev_server_port">8081</integer><integer name="react_native_inspector_proxy_port">8081</integer></file></source><source path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\google-services\debug"><file path="D:\Freelance\MotelManagement\client\android\app\build\generated\res\google-services\debug\values\values.xml" qualifiers=""><string name="default_web_client_id" translatable="false">728339198977-qnipi0l8mj5lcblnq1gm1pd1kupse12p.apps.googleusercontent.com</string><string name="gcm_defaultSenderId" translatable="false">728339198977</string><string name="google_api_key" translatable="false">AIzaSyBuildPlaceholderKeyForDevelopment</string><string name="google_app_id" translatable="false">1:728339198977:android:abc123def456789</string><string name="google_crash_reporting_api_key" translatable="false">AIzaSyBuildPlaceholderKeyForDevelopment</string><string name="google_storage_bucket" translatable="false">quanlynhatro-8afcf.appspot.com</string><string name="project_id" translatable="false">quanlynhatro-8afcf</string></file></source></dataSet><mergedItems/></merger>