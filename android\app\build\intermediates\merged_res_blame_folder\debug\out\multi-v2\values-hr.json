{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,131", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3586,3684,3791,3888,3987,4091,4195,11157", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3679,3786,3883,3982,4086,4190,4307,11253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,320,398,476,561,658,751,847,977,1061,1129,1225,1293,1356,1464,1524,1590,1646,1717,1777,1831,1957,2014,2076,2130,2205,2339,2424,2505,2642,2726,2812,2903,2959,3014,3080,3154,3232,3320,3392,3469,3549,3623,3716,3789,3881,3977,4051,4127,4223,4275,4342,4429,4516,4578,4642,4705,4811,4912,5009,5113,5173,5232", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "315,393,471,556,653,746,842,972,1056,1124,1220,1288,1351,1459,1519,1585,1641,1712,1772,1826,1952,2009,2071,2125,2200,2334,2419,2500,2637,2721,2807,2898,2954,3009,3075,3149,3227,3315,3387,3464,3544,3618,3711,3784,3876,3972,4046,4122,4218,4270,4337,4424,4511,4573,4637,4700,4806,4907,5004,5108,5168,5227,5307"}, "to": {"startLines": "2,35,36,37,38,39,49,50,51,54,55,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3155,3233,3311,3396,3493,4505,4601,4731,5004,5072,5558,5776,5906,6014,6074,6140,6196,6267,6327,6381,6507,6564,6626,6680,6755,7124,7209,7290,7427,7511,7597,7688,7744,7799,7865,7939,8017,8105,8177,8254,8334,8408,8501,8574,8666,8762,8836,8912,9008,9060,9127,9214,9301,9363,9427,9490,9596,9697,9794,9898,9958,10017", "endLines": "6,35,36,37,38,39,49,50,51,54,55,60,63,65,66,67,68,69,70,71,72,73,74,75,76,77,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "endColumns": "12,77,77,84,96,92,95,129,83,67,95,67,62,107,59,65,55,70,59,53,125,56,61,53,74,133,84,80,136,83,85,90,55,54,65,73,77,87,71,76,79,73,92,72,91,95,73,75,95,51,66,86,86,61,63,62,105,100,96,103,59,58,79", "endOffsets": "365,3228,3306,3391,3488,3581,4596,4726,4810,5067,5163,5621,5834,6009,6069,6135,6191,6262,6322,6376,6502,6559,6621,6675,6750,6884,7204,7285,7422,7506,7592,7683,7739,7794,7860,7934,8012,8100,8172,8249,8329,8403,8496,8569,8661,8757,8831,8907,9003,9055,9122,9209,9296,9358,9422,9485,9591,9692,9789,9893,9953,10012,10092"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,232,322,406,480,549,630,697,768,849,932,1016,1105,1177,1263,1346,1422,1502,1584,1663,1741,1817,1907,1980,2059,2137", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "124,227,317,401,475,544,625,692,763,844,927,1011,1100,1172,1258,1341,1417,1497,1579,1658,1736,1812,1902,1975,2054,2132,2213"}, "to": {"startLines": "34,47,48,52,59,61,62,64,78,79,80,118,119,120,121,123,124,125,126,127,128,129,130,132,133,134,135", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3081,4312,4415,4815,5484,5626,5695,5839,6889,6960,7041,10097,10181,10270,10342,10513,10596,10672,10752,10834,10913,10991,11067,11258,11331,11410,11488", "endColumns": "73,102,89,83,73,68,80,66,70,80,82,83,88,71,85,82,75,79,81,78,77,75,89,72,78,77,80", "endOffsets": "3150,4410,4500,4894,5553,5690,5771,5901,6955,7036,7119,10176,10265,10337,10423,10591,10667,10747,10829,10908,10986,11062,11152,11326,11405,11483,11564"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,260,374", "endColumns": "104,99,113,101", "endOffsets": "155,255,369,471"}, "to": {"startLines": "53,56,57,58", "startColumns": "4,4,4,4", "startOffsets": "4899,5168,5268,5382", "endColumns": "104,99,113,101", "endOffsets": "4999,5263,5377,5479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "370,475,570,677,763,867,986,1071,1153,1244,1337,1432,1526,1626,1719,1814,1909,2000,2091,2177,2281,2393,2494,2599,2713,2815,2984,10428", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "470,565,672,758,862,981,1066,1148,1239,1332,1427,1521,1621,1714,1809,1904,1995,2086,2172,2276,2388,2489,2594,2708,2810,2979,3076,10508"}}]}]}