import React, { useState } from "react";
import { View, StyleSheet, TouchableOpacity, Image, Alert } from "react-native";
import { Text, TextInput, ActivityIndicator } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors } from "../../theme";
import { auth } from "../../../config/firebase";
import { sendPasswordResetEmail } from "firebase/auth";

const ForgotPasswordScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();

  const [email, setEmail] = useState("");
  const [resetSent, setResetSent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleResetPassword = async () => {
    if (!email) {
      Alert.alert("Lỗi", "Vui lòng nhập email của bạn");
      return;
    }

    // Kiểm tra định dạng email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      Alert.alert("Lỗi", "Email không hợp lệ");
      return;
    }

    setIsLoading(true);

    try {
      await sendPasswordResetEmail(auth, email);
      setIsLoading(false);
      setResetSent(true);
    } catch (error: any) {
      setIsLoading(false);
      let errorMessage = "Đã có lỗi xảy ra khi gửi email đặt lại mật khẩu";

      if (error.code === "auth/user-not-found") {
        errorMessage = "Không tìm thấy tài khoản với email này";
      } else if (error.code === "auth/invalid-email") {
        errorMessage = "Email không hợp lệ";
      }

      Alert.alert("Lỗi", errorMessage);
    }
  };

  return (
    <View style={styles.container}>
      <SafeAreaView style={styles.header}>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/login.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </SafeAreaView>

      <View style={styles.formContainer}>
        {resetSent ? (
          <View style={styles.successContainer}>
            <MaterialCommunityIcons
              name="check-circle"
              size={60}
              color={Colors.SUCCESS}
              style={styles.successIcon}
            />
            <Text style={styles.successTitle}>Email đã được gửi!</Text>
            <Text style={styles.successText}>
              Chúng tôi đã gửi hướng dẫn đặt lại mật khẩu đến email của bạn. Vui
              lòng kiểm tra hộp thư đến.
            </Text>
            <TouchableOpacity
              style={styles.backToLoginButton}
              onPress={() => navigation.navigate("Login")}
            >
              <Text style={styles.backToLoginText}>Quay lại đăng nhập</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            <View style={styles.inputContainer}>
              <Text style={styles.inputLabel}>Email</Text>
              <TextInput
                style={styles.input}
                placeholder="Nhập email của bạn"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                mode="outlined"
                outlineColor={Colors.GRAY_MEDIUM}
                activeOutlineColor={Colors.PRIMARY}
              />
            </View>

            <TouchableOpacity
              style={styles.resetButton}
              onPress={handleResetPassword}
              disabled={isLoading}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.WHITE} size="small" />
              ) : (
                <Text style={styles.resetButtonText}>Đặt lại mật khẩu</Text>
              )}
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.backToLoginLink}
              onPress={() => navigation.navigate("Login")}
            >
              <Text style={styles.backToLoginLinkText}>Quay lại đăng nhập</Text>
            </TouchableOpacity>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
  },
  header: {
    paddingTop: 20,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 200,
    height: 150,
  },
  formContainer: {
    flex: 1,
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: 30,
    borderTopRightRadius: 30,
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 15,
    marginTop: 10,
  },
  description: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 20,
  },
  errorContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#FFEBEE",
    padding: 10,
    borderRadius: 5,
    marginBottom: 15,
  },
  errorText: {
    color: Colors.DANGER,
    flex: 1,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 5,
    marginLeft: 5,
  },
  input: {
    backgroundColor: Colors.WHITE,
  },
  resetButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    marginBottom: 20,
  },
  resetButtonText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
  backToLoginLink: {
    alignItems: "center",
  },
  backToLoginLinkText: {
    color: Colors.PRIMARY,
    fontSize: 16,
  },
  successContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 20,
  },
  successIcon: {
    marginBottom: 20,
  },
  successTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.TEXT_PRIMARY,
    marginBottom: 10,
  },
  successText: {
    fontSize: 16,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
    marginBottom: 30,
  },
  backToLoginButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: 10,
    padding: 15,
    alignItems: "center",
    width: "100%",
  },
  backToLoginText: {
    color: Colors.WHITE,
    fontSize: 16,
    fontWeight: "bold",
  },
});

export default ForgotPasswordScreen;
