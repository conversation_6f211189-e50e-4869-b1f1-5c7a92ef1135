// Models based on the ERD diagram

// User model
export interface User {
  user_id: string;
  email: string;
  name: string;
  phone: string;
  managed_motels: string[]; // List of motel IDs
  created_at: Date;
  role: 'owner' | 'manager';
}

// Motel model
export interface Motel {
  motel_id: string;
  owner_id: string; // Reference to User
  name: string;
  address: string;
  managers: string[]; // List of user IDs
  created_at: Date;
}

// Room model
export interface Room {
  room_id: string;
  motel_id: string; // Reference to Motel
  room_number: string;
  gia: number; // Price
  tienCoc: number; // Deposit
  tenant_id: string | null; // Reference to current tenant (if any)
  created_at: Date;
  start_date: Date | null;
  end_date: Date | null;
  ghiChu: string; // Notes
  dienTich: number; // Area in square meters
  loaiPhong: string[]; // Room types
  tienNghi: string[]; // Amenities
  status: 'available' | 'occupied' | 'maintenance';
}

// Resident model
export interface Resident {
  resident_id: string;
  room_id: string; // Reference to Room
  name: string;
  phone: string;
  identity_number: string;
  is_main_tenant: boolean;
  created_at: Date;
}

// GiaDienNuoc model (Electricity and Water Pricing)
export interface GiaDienNuoc {
  motel_id: string; // Reference to Motel
  electricity_price: number;
  water_price: number;
  updated_at: Date;
}

// ChiSoDienNuoc model (Electricity and Water Readings)
export interface ChiSoDienNuoc {
  record_id: string;
  phong_id: string; // Reference to Room
  motel_id: string; // Reference to Motel
  thang_nam: string; // Month/Year
  chi_so_dien_cu: number; // Previous electricity reading
  chi_so_dien_moi: number; // New electricity reading
  chi_so_nuoc_cu: number; // Previous water reading
  chi_so_nuoc_moi: number; // New water reading
  created_at: Date;
}

// HoaDon model (Invoice/Bill)
export interface HoaDon {
  hoadon_id: string;
  phong_id: string; // Reference to Room
  motel_id: string; // Reference to Room
  tenant_id: string; // Reference to User (tenant)
  thang_hoa_don: string; // Month of the bill
  tienphong: number; // Room fee
  tiendien: number; // Electricity fee
  tiennuoc: number; // Water fee
  additional_fees: AdditionalFee[]; // Additional fees
  tong_tien: number; // Total amount
  tiennuoc_moi: number; // New water fee
  auto_generated: boolean;
  created_at: Date;
  due_date: Date;
  overdue_days: number;
  payment_status: 'pending' | 'paid' | 'overdue';
}

// Additional fee interface
export interface AdditionalFee {
  name: string;
  amount: number;
}

// AuditLog model
export interface AuditLog {
  log_id: string;
  user_id: string; // Reference to User
  entity_id: string; // ID of the entity being modified
  old_value: any; // Previous value
  new_value: any; // New value
  timestamp: Date;
  entity_type: 'Rooms' | 'HoaDon' | 'ChiSoDienNuoc';
  action: 'CREATE' | 'UPDATE' | 'DELETE';
}
