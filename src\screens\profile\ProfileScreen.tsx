import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Surface,
  List,
  Divider,
  Button,
  Portal,
  Dialog,
  TextInput,
  Switch,
  RadioButton,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { auth } from "../../../config/firebase";
import { signOut } from "firebase/auth";
import { useLanguage } from "../../context/LanguageContext";
import { useTheme } from "../../context/ThemeContext";

const ProfileScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { currentLanguage, changeLanguage, t } = useLanguage();
  const { isDarkMode, toggleTheme } = useTheme();

  // States for dialogs
  const [passwordDialogVisible, setPasswordDialogVisible] = useState(false);
  const [languageDialogVisible, setLanguageDialogVisible] = useState(false);

  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  const [selectedLanguage, setSelectedLanguage] = useState(currentLanguage);

  // Update selectedLanguage when currentLanguage changes
  useEffect(() => {
    setSelectedLanguage(currentLanguage);
  }, [currentLanguage]);

  const user = {
    name: "An",
    email: "<EMAIL>",
    role: t("profile.role"),
    avatar: "https://randomuser.me/api/portraits/men/32.jpg", // Using a URL for the avatar
  };

  const squareMenuItems = [
    {
      title: t("profile.profile"),
      icon: "account-outline",
      onPress: () => navigation.navigate("UpdatePersonalInfo"),
    },
    {
      title: t("profile.building"),
      icon: "office-building-outline",
      onPress: () => navigation.navigate("MotelsManagement"),
    },
    {
      title: t("profile.editHistory"),
      icon: "history",
      onPress: () => console.log("Edit history pressed"),
    },
    {
      title: t("profile.privacyPolicy"),
      icon: "shield-account-outline",
      onPress: () => console.log("Privacy policy pressed"),
    },
  ];

  const listMenuItems = [
    {
      title: t("profile.changePassword"),
      icon: "lock-outline",
      onPress: () => setPasswordDialogVisible(true),
      type: "dialog",
    },
    {
      title: t("profile.language"),
      icon: "translate",
      onPress: () => setLanguageDialogVisible(true),
      type: "dialog",
    },
    {
      title: t("profile.darkMode"),
      icon: "theme-light-dark",
      onPress: toggleTheme,
      type: "toggle",
      value: isDarkMode,
    },
    {
      title: t("profile.termsOfUse"),
      icon: "file-document-outline",
      onPress: () => console.log("Terms of use pressed"),
      type: "navigate",
    },
  ];

  const handleLogout = async () => {
    try {
      await signOut(auth);
      // Firebase Auth's onAuthStateChanged listener in RootNavigator 
      // will automatically redirect to Auth navigator
    } catch (error) {
      Alert.alert(
        "Lỗi",
        "Có lỗi xảy ra khi đăng xuất. Vui lòng thử lại."
      );
    }
  };

  const handleChangePassword = () => {
    console.log("Changing password", {
      currentPassword,
      newPassword,
      confirmPassword,
    });
    setPasswordDialogVisible(false);
    // Reset fields
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
  };

  const handleChangeLanguage = () => {
    // Apply the language change
    changeLanguage(selectedLanguage);
    setLanguageDialogVisible(false);
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      <StatusBar
        backgroundColor={Colors.PRIMARY}
        barStyle={isDarkMode ? "light-content" : "dark-content"}
      />

      <ScrollView style={styles.content}>
        <Surface
          style={[styles.userCard, isDarkMode && styles.darkSurface]}
          elevation={1}
        >
          <View style={styles.userInfo}>
            <Image source={{ uri: user.avatar }} style={styles.avatar} />
            <View style={styles.userDetails}>
              <Text
                variant="titleLarge"
                style={[styles.userName, isDarkMode && styles.darkText]}
              >
                {user.name}
              </Text>
              <Text
                variant="bodyMedium"
                style={[
                  styles.userEmail,
                  isDarkMode && styles.darkSecondaryText,
                ]}
              >
                {user.email}
              </Text>
              <Text variant="bodySmall" style={styles.userRole}>
                {user.role}
              </Text>
            </View>
          </View>
        </Surface>

        <View style={styles.squareMenuContainer}>
          {squareMenuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.squareMenuItem, isDarkMode && styles.darkSurface]}
              onPress={item.onPress}
            >
              <View
                style={[
                  styles.squareMenuIconContainer,
                  isDarkMode && styles.darkIconContainer,
                ]}
              >
                <MaterialCommunityIcons
                  name={item.icon as any}
                  size={28}
                  color={Colors.PRIMARY}
                />
              </View>
              <Text
                style={[styles.squareMenuText, isDarkMode && styles.darkText]}
              >
                {item.title}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <Surface
          style={[styles.menuCard, isDarkMode && styles.darkSurface]}
          elevation={1}
        >
          {listMenuItems.map((item, index) => (
            <React.Fragment key={index}>
              <List.Item
                title={item.title}
                left={() => (
                  <MaterialCommunityIcons
                    name={item.icon as any}
                    size={24}
                    color={Colors.PRIMARY}
                    style={styles.menuIcon}
                  />
                )}
                right={() =>
                  item.type === "toggle" ? (
                    <Switch
                      value={item.value}
                      onValueChange={item.onPress}
                      color={Colors.PRIMARY}
                      style={{
                        height: 24,
                      }}
                    />
                  ) : (
                    <MaterialCommunityIcons
                      name="chevron-right"
                      size={24}
                      color={
                        isDarkMode ? Colors.DARK.GRAY_DARK : Colors.GRAY_DARK
                      }
                    />
                  )
                }
                onPress={item.onPress}
                style={styles.menuItem}
                titleStyle={[
                  styles.menuItemTitle,
                  isDarkMode && styles.darkText,
                ]}
              />
              {index < listMenuItems.length - 1 && (
                <Divider
                  style={[styles.divider, isDarkMode && styles.darkDivider]}
                />
              )}
            </React.Fragment>
          ))}
        </Surface>

        <Button
          mode="contained"
          icon="logout"
          onPress={handleLogout}
          style={styles.logoutButton}
          buttonColor={Colors.DANGER}
        >
          {t("auth.logout")}
        </Button>

        <View style={styles.versionContainer}>
          <Text
            variant="bodySmall"
            style={[styles.versionText, isDarkMode && styles.darkHintText]}
          >
            {t("profile.version")} 1.0.0
          </Text>
        </View>
      </ScrollView>

      <Portal>
        <Dialog
          visible={passwordDialogVisible}
          onDismiss={() => setPasswordDialogVisible(false)}
          style={[styles.dialog, isDarkMode && styles.darkDialog]}
        >
          <Dialog.Title>{t("profile.changePassword")}</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label={t("auth.currentPassword")}
              value={currentPassword}
              onChangeText={setCurrentPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
            <TextInput
              label={t("auth.newPassword")}
              value={newPassword}
              onChangeText={setNewPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
            <TextInput
              label={t("auth.confirmPassword")}
              value={confirmPassword}
              onChangeText={setConfirmPassword}
              secureTextEntry
              style={styles.dialogInput}
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setPasswordDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleChangePassword}>
              {t("common.confirm")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={languageDialogVisible}
          onDismiss={() => setLanguageDialogVisible(false)}
          style={[styles.dialog, isDarkMode && styles.darkDialog]}
        >
          <Dialog.Title>{t("profile.language")}</Dialog.Title>
          <Dialog.Content>
            <RadioButton.Group
              onValueChange={setSelectedLanguage}
              value={selectedLanguage}
            >
              <View style={styles.radioItem}>
                <RadioButton value="vi" color={Colors.PRIMARY} />
                <Text style={isDarkMode ? styles.darkText : undefined}>
                  Tiếng Việt
                </Text>
              </View>
              <View style={styles.radioItem}>
                <RadioButton value="en" color={Colors.PRIMARY} />
                <Text style={isDarkMode ? styles.darkText : undefined}>
                  English
                </Text>
              </View>
            </RadioButton.Group>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setLanguageDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleChangeLanguage}>
              {t("common.confirm")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  darkContainer: {
    backgroundColor: Colors.DARK.BACKGROUND,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 32,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  userCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    backgroundColor: Colors.WHITE,
  },
  darkSurface: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  userInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginRight: 16,
  },
  userDetails: {
    flex: 1,
  },
  userName: {
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
  },
  darkText: {
    color: Colors.DARK.TEXT_PRIMARY,
  },
  userEmail: {
    color: Colors.TEXT_SECONDARY,
    marginTop: 4,
  },
  darkSecondaryText: {
    color: Colors.DARK.TEXT_SECONDARY,
  },
  darkHintText: {
    color: Colors.DARK.TEXT_HINT,
  },
  userRole: {
    color: Colors.PRIMARY,
    marginTop: 4,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  squareMenuContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  squareMenuItem: {
    width: "48%",
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    alignItems: "center",
    justifyContent: "center",
    elevation: 1,
  },
  squareMenuIconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.GRAY_LIGHT,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 8,
  },
  darkIconContainer: {
    backgroundColor: Colors.DARK.GRAY_LIGHT,
  },
  squareMenuText: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_PRIMARY,
    textAlign: "center",
    marginTop: 4,
  },
  menuCard: {
    borderRadius: 12,
    marginBottom: 16,
    overflow: "hidden",
    backgroundColor: Colors.WHITE,
  },
  menuItem: {
    paddingVertical: 12,
  },
  menuIcon: {
    marginLeft: 8,
  },
  menuItemTitle: {
    fontSize: Typography.FONT_SIZE.lg,
    color: Colors.TEXT_PRIMARY,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.GRAY_MEDIUM,
  },
  darkDivider: {
    backgroundColor: Colors.DARK.DIVIDER,
  },
  dialog: {
    borderRadius: 12,
    backgroundColor: Colors.WHITE,
  },
  darkDialog: {
    backgroundColor: Colors.DARK.CARD_BACKGROUND,
  },
  dialogInput: {
    marginBottom: 12,
  },
  radioItem: {
    flexDirection: "row",
    alignItems: "center",
    marginVertical: 4,
  },
  logoutButton: {
    marginVertical: 16,
    borderRadius: 8,
    paddingVertical: 8,
  },
  versionContainer: {
    alignItems: "center",
    marginBottom: 24,
  },
  versionText: {
    color: Colors.TEXT_HINT,
  },
});

export default ProfileScreen;
