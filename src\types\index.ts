export type RootStackParamList = {
  Auth: undefined;
  App: undefined;
  RoomDetails: {
    roomId: string;
    roomNumber: string;
    motelId: string;
    initialStatus: "available" | "occupied" | "maintenance";
    price: number;
    deposit: number;
    area: number;
    amenities: string[];
    roomTypes: string[];
    note: string;
  };
  AddRoom: undefined;
  MotelsManagement: undefined;
  AddEditMotel: {
    motelId?: string; // undefined for create, string for edit
    mode: "create" | "edit";
  };
  UpdatePersonalInfo: undefined;
};

export type AuthStackParamList = {
  Welcome: undefined;
  Login: undefined;
  SignUp: undefined;
  ForgotPassword: undefined;
  OTPVerification: {
    email: string;
    flow: "signup" | "forgotPassword";
    registrationData?: {
      name: string;
      email: string;
      password: string;
      verificationCode: string;
    };
  };
};

export type AppStackParamList = {
  MainTabs: undefined;
  AddRoom: undefined;
  RoomDetails: { roomId?: string };
  MotelsManagement: undefined;
  AddEditMotel: {
    motelId?: string; // undefined for create, string for edit
    mode: "create" | "edit";
  };
  UpdatePersonalInfo: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Report: undefined;
  Profile: undefined;
};

// Export all models
export * from "./models";
export * from "./auth";
