// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { initializeAuth, getReactNativePersistence } from "firebase/auth";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getFirestore } from "firebase/firestore";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyAMVmWB9jAM7_EcQm1HT0Yh1ZFTHXlyn8w",
  authDomain: "quanlynhatro-8afcf.firebaseapp.com",
  projectId: "quanlynhatro-8afcf",
  storageBucket: "quanlynhatro-8afcf.firebasestorage.app",
  messagingSenderId: "728339198977",
  appId: "1:728339198977:web:6e5af7685ec6ec31b37c00",
  measurementId: "G-2YP5SBHSV1",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with AsyncStorage persistence
export const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(AsyncStorage),
});

// Initialize Firestore
export const db = getFirestore(app);
