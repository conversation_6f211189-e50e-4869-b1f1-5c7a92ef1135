import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";

const firebaseConfig = {
  apiKey: "AIzaSyAMVmWB9jAM7_EcQm1HT0Yh1ZFTHXlyn8w",
  authDomain: "quanlynhatro-8afcf.firebaseapp.com",
  projectId: "quanlynhatro-8afcf",
  storageBucket: "quanlynhatro-8afcf.firebasestorage.app",
  messagingSenderId: "728339198977",
  appId: "1:728339198977:web:6e5af7685ec6ec31b37c00",
  measurementId: "G-2YP5SBHSV1",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth - Firebase 11+ handles React Native persistence automatically
// when @react-native-async-storage/async-storage is installed
export const auth = getAuth(app);

// Initialize Firestore
export const db = getFirestore(app);
