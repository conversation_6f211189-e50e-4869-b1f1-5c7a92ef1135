import { initializeApp } from "firebase/app";
import { initializeAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
// @ts-ignore - Firebase 11.6.0 export issue
import { getReactNativePersistence } from "firebase/auth";
import ReactNativeAsyncStorage from "@react-native-async-storage/async-storage";

const firebaseConfig = {
  apiKey: "AIzaSyAMVmWB9jAM7_EcQm1HT0Yh1ZFTHXlyn8w",
  authDomain: "quanlynhatro-8afcf.firebaseapp.com",
  projectId: "quanlynhatro-8afcf",
  storageBucket: "quanlynhatro-8afcf.firebasestorage.app",
  messagingSenderId: "728339198977",
  appId: "1:728339198977:web:6e5af7685ec6ec31b37c00",
  measurementId: "G-2YP5SBHSV1",
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Auth with AsyncStorage persistence for React Native
export const auth = initializeAuth(app, {
  persistence: getReactNativePersistence(ReactNativeAsyncStorage),
});

// Initialize Firestore
export const db = getFirestore(app);
