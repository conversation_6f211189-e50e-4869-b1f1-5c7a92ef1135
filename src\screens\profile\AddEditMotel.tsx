import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Alert,
  TouchableOpacity,
} from "react-native";
import {
  Text,
  Surface,
  IconButton,
  TextInput,
  Button,
  Chip,
  Divider,
  Dialog,
  Portal,
  List,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { useApp } from "../../context/AppContext";

type AddEditMotelRouteProp = RouteProp<RootStackParamList, "AddEditMotel">;
type AddEditMotelNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  "AddEditMotel"
>;

interface Manager {
  email: string;
  name?: string;
  status: "pending" | "accepted" | "invalid";
}

export default function AddEditMotel() {
  const navigation = useNavigation<AddEditMotelNavigationProp>();
  const route = useRoute<AddEditMotelRouteProp>();
  const { colors } = useApp();

  const { mode, motelId } = route.params;
  const isEditMode = mode === "edit";

  // Form state
  const [motelName, setMotelName] = useState("");
  const [address, setAddress] = useState("");
  const [managers, setManagers] = useState<Manager[]>([]);
  const [loading, setLoading] = useState(false);

  // Dialog states
  const [showAddManagerDialog, setShowAddManagerDialog] = useState(false);
  const [newManagerEmail, setNewManagerEmail] = useState("");
  const [emailError, setEmailError] = useState("");

  // Form validation
  const [nameError, setNameError] = useState("");
  const [addressError, setAddressError] = useState("");

  useEffect(() => {
    if (isEditMode && motelId) {
      // Load motel data for editing
      loadMotelData(motelId);
    }
  }, [isEditMode, motelId]);

  const loadMotelData = async (id: string) => {
    setLoading(true);
    try {
      // Mock data for editing
      setTimeout(() => {
        setMotelName("Nhà trọ Sinh viên");
        setAddress("123 Nguyễn Văn Cừ, Quận 5, TP.HCM");
        setManagers([
          {
            email: "<EMAIL>",
            name: "Nguyễn Văn A",
            status: "accepted",
          },
          { email: "<EMAIL>", status: "pending" },
        ]);
        setLoading(false);
      }, 1000);
    } catch (error) {
      setLoading(false);
      Alert.alert("Lỗi", "Không thể tải thông tin nhà trọ");
    }
  };

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleAddManager = () => {
    setEmailError("");

    if (!newManagerEmail.trim()) {
      setEmailError("Vui lòng nhập email");
      return;
    }

    if (!validateEmail(newManagerEmail)) {
      setEmailError("Email không hợp lệ");
      return;
    }

    // Check if email already exists
    if (managers.some((manager) => manager.email === newManagerEmail)) {
      setEmailError("Email này đã được thêm");
      return;
    }

    // Add new manager
    const newManager: Manager = {
      email: newManagerEmail,
      status: "pending",
    };

    setManagers([...managers, newManager]);
    setNewManagerEmail("");
    setShowAddManagerDialog(false);
  };

  const handleRemoveManager = (email: string) => {
    Alert.alert("Xác nhận", "Bạn có chắc chắn muốn xóa người quản lý này?", [
      { text: "Hủy", style: "cancel" },
      {
        text: "Xóa",
        style: "destructive",
        onPress: () => {
          setManagers(managers.filter((manager) => manager.email !== email));
        },
      },
    ]);
  };

  const validateForm = (): boolean => {
    let isValid = true;

    if (!motelName.trim()) {
      setNameError("Vui lòng nhập tên nhà trọ");
      isValid = false;
    } else {
      setNameError("");
    }

    if (!address.trim()) {
      setAddressError("Vui lòng nhập địa chỉ");
      isValid = false;
    } else {
      setAddressError("");
    }

    return isValid;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Mock save operation
      setTimeout(() => {
        setLoading(false);
        Alert.alert(
          "Thành công",
          isEditMode ? "Cập nhật nhà trọ thành công" : "Tạo nhà trọ thành công",
          [
            {
              text: "OK",
              onPress: () => navigation.goBack(),
            },
          ]
        );
      }, 1500);
    } catch (error) {
      setLoading(false);
      Alert.alert("Lỗi", "Có lỗi xảy ra, vui lòng thử lại");
    }
  };

  const getManagerStatusInfo = (status: Manager["status"]) => {
    switch (status) {
      case "accepted":
        return {
          text: "Đã chấp nhận",
          color: colors.success,
          icon: "check-circle",
        };
      case "pending":
        return {
          text: "Chờ xác nhận",
          color: colors.warning,
          icon: "clock-outline",
        };
      case "invalid":
        return {
          text: "Email không tồn tại",
          color: colors.error,
          icon: "alert-circle",
        };
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <StatusBar backgroundColor={colors.primary} barStyle="light-content" />

      {/* Header */}
      <Surface
        style={[styles.header, { backgroundColor: colors.primary }]}
        elevation={2}
      >
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            iconColor={colors.onPrimary}
            size={24}
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
          <Text
            variant="headlineSmall"
            style={[styles.headerTitle, { color: colors.onPrimary }]}
          >
            {isEditMode ? "Chỉnh sửa nhà trọ" : "Thêm nhà trọ mới"}
          </Text>
        </View>
      </Surface>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Basic Information */}
        <Surface
          style={[styles.section, { backgroundColor: colors.surface }]}
          elevation={1}
        >
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: colors.onSurface }]}
          >
            Thông tin cơ bản
          </Text>

          <TextInput
            label="Tên nhà trọ *"
            value={motelName}
            onChangeText={setMotelName}
            mode="outlined"
            style={styles.input}
            error={!!nameError}
            disabled={loading}
          />
          {nameError ? (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {nameError}
            </Text>
          ) : null}

          <TextInput
            label="Địa chỉ *"
            value={address}
            onChangeText={setAddress}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            error={!!addressError}
            disabled={loading}
          />
          {addressError ? (
            <Text style={[styles.errorText, { color: colors.error }]}>
              {addressError}
            </Text>
          ) : null}
        </Surface>

        {/* Shared Management */}
        <Surface
          style={[styles.section, { backgroundColor: colors.surface }]}
          elevation={1}
        >
          <View style={styles.sectionHeader}>
            <View>
              <Text
                variant="titleMedium"
                style={[styles.sectionTitle, { color: colors.onSurface }]}
              >
                Quản lý chung
              </Text>
              <Text
                variant="bodySmall"
                style={[
                  styles.sectionSubtitle,
                  { color: colors.onSurfaceVariant },
                ]}
              >
                Thêm email của người khác để cùng quản lý nhà trọ
              </Text>
            </View>
            <IconButton
              icon="plus"
              iconColor={colors.primary}
              size={24}
              onPress={() => setShowAddManagerDialog(true)}
              disabled={loading}
            />
          </View>

          {managers.length > 0 ? (
            <View style={styles.managersList}>
              {managers.map((manager, index) => {
                const statusInfo = getManagerStatusInfo(manager.status);
                return (
                  <View key={manager.email} style={styles.managerItem}>
                    <View style={styles.managerInfo}>
                      <MaterialCommunityIcons
                        name="account"
                        size={20}
                        color={colors.onSurfaceVariant}
                        style={styles.managerIcon}
                      />
                      <View style={styles.managerDetails}>
                        <Text
                          style={[
                            styles.managerEmail,
                            { color: colors.onSurface },
                          ]}
                        >
                          {manager.email}
                        </Text>
                        {manager.name && (
                          <Text
                            style={[
                              styles.managerName,
                              { color: colors.onSurfaceVariant },
                            ]}
                          >
                            {manager.name}
                          </Text>
                        )}
                      </View>
                    </View>

                    <View style={styles.managerActions}>
                      <Chip
                        icon={statusInfo.icon}
                        style={[
                          styles.statusChip,
                          { backgroundColor: statusInfo.color + "20" },
                        ]}
                        textStyle={[
                          styles.statusChipText,
                          { color: statusInfo.color },
                        ]}
                        compact
                      >
                        {statusInfo.text}
                      </Chip>
                      <IconButton
                        icon="delete"
                        iconColor={colors.error}
                        size={20}
                        onPress={() => handleRemoveManager(manager.email)}
                        disabled={loading}
                      />
                    </View>
                  </View>
                );
              })}
            </View>
          ) : (
            <View style={styles.emptyManagers}>
              <MaterialCommunityIcons
                name="account-multiple-plus"
                size={48}
                color={colors.onSurfaceVariant}
              />
              <Text
                style={[styles.emptyText, { color: colors.onSurfaceVariant }]}
              >
                Chưa có người quản lý nào
              </Text>
              <Text
                style={[
                  styles.emptySubtext,
                  { color: colors.onSurfaceVariant },
                ]}
              >
                Thêm email để mời người khác cùng quản lý
              </Text>
            </View>
          )}
        </Surface>
      </ScrollView>

      {/* Save Button */}
      <Surface
        style={[styles.footer, { backgroundColor: colors.surface }]}
        elevation={3}
      >
        <Button
          mode="contained"
          onPress={handleSave}
          loading={loading}
          disabled={loading}
          style={[styles.saveButton, { backgroundColor: colors.primary }]}
          contentStyle={styles.saveButtonContent}
        >
          {isEditMode ? "Cập nhật" : "Tạo nhà trọ"}
        </Button>
      </Surface>

      {/* Add Manager Dialog */}
      <Portal>
        <Dialog
          visible={showAddManagerDialog}
          onDismiss={() => setShowAddManagerDialog(false)}
          style={{ backgroundColor: colors.surface }}
        >
          <Dialog.Title style={{ color: colors.onSurface }}>
            Thêm người quản lý
          </Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Email"
              value={newManagerEmail}
              onChangeText={(text) => {
                setNewManagerEmail(text);
                setEmailError("");
              }}
              mode="outlined"
              keyboardType="email-address"
              autoCapitalize="none"
              error={!!emailError}
              placeholder="<EMAIL>"
            />
            {emailError ? (
              <Text style={[styles.errorText, { color: colors.error }]}>
                {emailError}
              </Text>
            ) : null}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowAddManagerDialog(false)}>Hủy</Button>
            <Button onPress={handleAddManager}>Thêm</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 16,
    paddingBottom: 16,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontWeight: Typography.FONT_WEIGHT.bold,
    marginBottom: 4,
  },
  sectionSubtitle: {
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 16,
  },
  input: {
    marginBottom: 8,
  },
  errorText: {
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 8,
    marginLeft: 4,
  },
  managersList: {
    gap: 12,
  },
  managerItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 8,
    backgroundColor: Colors.GRAY_LIGHT + "40",
  },
  managerInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  managerIcon: {
    marginRight: 12,
  },
  managerDetails: {
    flex: 1,
  },
  managerEmail: {
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  managerName: {
    fontSize: Typography.FONT_SIZE.sm,
    marginTop: 2,
  },
  managerActions: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  statusChip: {
    height: 28,
  },
  statusChipText: {
    fontSize: Typography.FONT_SIZE.xs,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  emptyManagers: {
    alignItems: "center",
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.medium,
    marginTop: 12,
  },
  emptySubtext: {
    fontSize: Typography.FONT_SIZE.sm,
    textAlign: "center",
    marginTop: 4,
  },
  footer: {
    padding: 16,
    paddingBottom: 32,
  },
  saveButton: {
    borderRadius: 8,
  },
  saveButtonContent: {
    paddingVertical: 8,
  },
});
