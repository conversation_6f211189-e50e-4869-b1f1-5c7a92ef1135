{"name": "client", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@mdi/js": "^7.4.47", "@mdi/react": "^1.6.1", "@react-native-async-storage/async-storage": "^1.24.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-native-masked-view/masked-view": "^0.3.2", "@react-navigation/bottom-tabs": "^7.3.3", "@react-navigation/native": "^7.0.19", "@react-navigation/native-stack": "^7.3.3", "@react-navigation/stack": "^7.2.10", "@reduxjs/toolkit": "^2.6.1", "expo": "~52.0.42", "expo-auth-session": "~5.4.0", "expo-crypto": "~12.8.1", "expo-file-system": "~18.0.12", "expo-firebase-recaptcha": "^2.3.1", "expo-linear-gradient": "~14.0.2", "expo-media-library": "~17.0.6", "expo-sharing": "~13.0.1", "expo-status-bar": "~2.0.1", "firebase": "^11.6.0", "i18n-js": "^4.5.1", "react": "18.3.1", "react-native": "0.76.8", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "^2.25.0", "react-native-paper": "^5.13.1", "react-native-reanimated": "^3.17.5", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-view-shot": "^4.0.3", "react-native-webview": "^13.13.5", "react-redux": "^9.2.0", "victory-native": "^41.17.1", "expo-web-browser": "~14.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/i18n-js": "^4.0.1", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}