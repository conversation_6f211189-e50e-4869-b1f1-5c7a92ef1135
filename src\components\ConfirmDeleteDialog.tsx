import React from "react";
import {
  Dialog,
  Portal,
  Text,
  But<PERSON>,
  Divider,
} from "react-native-paper";
import { StyleSheet } from "react-native";
import { useTheme } from "../context/ThemeContext";
import { useLanguage } from "../context/LanguageContext";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface ConfirmDeleteDialogProps {
  visible: boolean;
  onDismiss: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  itemName?: string;
}

export default function ConfirmDeleteDialog({
  visible,
  onDismiss,
  onConfirm,
  title,
  message,
  itemName,
}: ConfirmDeleteDialogProps) {
  const { colors, isDarkMode } = useTheme();
  const { t } = useLanguage();

  const handleConfirm = () => {
    onConfirm();
    onDismiss();
  };

  return (
    <Portal>
      <Dialog
        visible={visible}
        onDismiss={onDismiss}
        style={[
          styles.dialog,
          { backgroundColor: isDarkMode ? colors.CARD_BACKGROUND : colors.WHITE },
        ]}
      >
        <Dialog.Icon
          icon={() => (
            <MaterialCommunityIcons
              name="alert-circle"
              size={48}
              color="#FF6B6B"
            />
          )}
        />
        
        <Dialog.Title style={[styles.title, { color: colors.TEXT_PRIMARY }]}>
          {title}
        </Dialog.Title>
        
        <Divider style={{ backgroundColor: colors.DIVIDER }} />
        
        <Dialog.Content style={styles.content}>
          <Text style={[styles.message, { color: colors.TEXT_SECONDARY }]}>
            {message}
          </Text>
          
          {itemName && (
            <Text style={[styles.itemName, { color: colors.TEXT_PRIMARY }]}>
              "{itemName}"
            </Text>
          )}
          
          <Text style={[styles.warning, { color: "#FF6B6B" }]}>
            {t("common.deleteWarning")}
          </Text>
        </Dialog.Content>

        <Dialog.Actions style={styles.actions}>
          <Button
            mode="outlined"
            onPress={onDismiss}
            textColor={colors.TEXT_SECONDARY}
            style={styles.cancelButton}
          >
            {t("common.cancel")}
          </Button>
          <Button
            mode="contained"
            onPress={handleConfirm}
            buttonColor="#FF6B6B"
            textColor={colors.WHITE}
            style={styles.deleteButton}
          >
            {t("common.delete")}
          </Button>
        </Dialog.Actions>
      </Dialog>
    </Portal>
  );
}

const styles = StyleSheet.create({
  dialog: {
    marginHorizontal: 20,
    borderRadius: 12,
  },
  title: {
    textAlign: "center",
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
  },
  content: {
    paddingVertical: 20,
    alignItems: "center",
  },
  message: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 12,
    lineHeight: 22,
  },
  itemName: {
    fontSize: 18,
    fontWeight: "600",
    textAlign: "center",
    marginBottom: 16,
  },
  warning: {
    fontSize: 14,
    textAlign: "center",
    fontStyle: "italic",
  },
  actions: {
    paddingHorizontal: 24,
    paddingBottom: 20,
    gap: 8,
  },
  cancelButton: {
    flex: 1,
  },
  deleteButton: {
    flex: 1,
  },
});
