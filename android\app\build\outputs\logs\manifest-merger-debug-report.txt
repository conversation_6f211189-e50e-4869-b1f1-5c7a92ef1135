-- Merging decision tree log ---
manifest
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:1:1-37:12
MERGED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:1:1-37:12
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:1:1-7:12
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:1:1-7:12
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\a870a614a2a06c3333f98bd77b21ae23\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-async-storage_async-storage] D:\Freelance\MotelManagement\client\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-masked-view_masked-view] D:\Freelance\MotelManagement\client\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo] D:\Freelance\MotelManagement\client\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-gesture-handler] D:\Freelance\MotelManagement\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-reanimated] D:\Freelance\MotelManagement\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-safe-area-context] D:\Freelance\MotelManagement\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-screens] D:\Freelance\MotelManagement\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-svg] D:\Freelance\MotelManagement\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-vector-icons] D:\Freelance\MotelManagement\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-view-shot] D:\Freelance\MotelManagement\client\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-19:12
MERGED from [:expo-application] D:\Freelance\MotelManagement\client\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-constants] D:\Freelance\MotelManagement\client\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-crypto] D:\Freelance\MotelManagement\client\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-9:12
MERGED from [:expo-eas-client] D:\Freelance\MotelManagement\client\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-33:12
MERGED from [:expo-font] D:\Freelance\MotelManagement\client\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-keep-awake] D:\Freelance\MotelManagement\client\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-linear-gradient] D:\Freelance\MotelManagement\client\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-14:12
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-29:12
MERGED from [:expo-structured-headers] D:\Freelance\MotelManagement\client\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-15:12
MERGED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-18:12
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:2:1-24:12
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e36c40fee784602eb08396fe737bc10e\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9952f23c7889cc5aea17b03fb07e26b7\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9c51337f957f47544c05cf8cf4133b64\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\4b0b4cd2d24d45c8c082cb3d9374c831\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-gif:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6841ca629dd99e9fcf6417289e24d\transformed\jetified-animated-gif-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ea510f873b60a34b61bac156c95e5df1\transformed\jetified-animated-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:webpsupport:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\20a3485050f8121eb7feba396a2800a0\transformed\jetified-webpsupport-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:animated-drawable:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9ebe7b5dc7b8de6d5417c17fba38a28a\transformed\jetified-animated-drawable-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:vito-options:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\62406b7471376d629a0a99248577b65e\transformed\jetified-vito-options-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ff0d475921ea02f1e153871151fa3385\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b2e5dc2bb6c4ae24ba71a02a8b6f8a19\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8882593734494990e8bbfd9a03a7c16a\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\13482a7c3fc1f087110086c4f267b08c\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\bcb653cf915589d42f0265d9b26dd2ea\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1842d5aaf193c3590824e8671e646f0\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\016c018e8cbf9f5e043d8943831595be\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee74f8dcbcd920820baee2163479df9e\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\26120840317a18bc9d9347cea1d73faa\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc396fa3573087d48bcd56df7ae0e564\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b0acc2ffa0da4a23d2d882a6a9162ace\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f153dbd822e587e820117285090a48da\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\650ae4f1204e322a49235413a6e2319a\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c9733fdc73e8ee7f4ca3ea81b9b7f38\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:8:1-13:12
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:8:1-16:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6780a2009a4dd79695e6581211bbd520\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\AndroidManifest.xml:17:1-26:12
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\52963e56ca573c7c394dc0939c7e7189\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:2:1-13:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\965ca604c3431eab8811bd7682895e8c\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a7124dc67ace9e9baf7b1abad3799cb\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da42cbaf7703b72f4f3ec0f19030b2e7\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de17595a4d979f58fd1f601ac0799b91\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd027cdca6a3814175c0e84b7a4c5d2e\transformed\jetified-activity-1.6.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d321e0369755bc3b82a1b538c01f9359\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8364e952541680155bc059ebe3ea4e98\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feb21053db54873509bb960ea0c337c4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17d0b8de03eb9ef3719cb0506b2e21aa\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\76961dff289650ce4988209cdd2f9f82\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f5fd5369f3ba90afba7fa67af778d07\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [com.facebook.fresco:vito-renderer:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\691b24e7e424686398ace54a44caf63b\transformed\jetified-vito-renderer-3.1.3\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.react:hermes-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\92dc0b6aadd0ed3b164a189f125eb83f\transformed\jetified-hermes-android-0.73.6-debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4188461951c9f776c3d1550af145b752\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\196777c3327b052411eae2e80d777398\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5340bca6a91b967163c8ef199ba62c17\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a02fcc903b26d99f6b23a1948ee57ab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe41b4c2d62e7dd412db20eb18527d61\transformed\webkit-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a160af3ac3ef548fc1eb1aec42f7a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1702ff7bcc4716f06294717707dd016f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d335db4f737189c64a1581c0213a89b4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e831abd82e09fbed4cbdd896e31662\transformed\browser-1.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00951d9a7646795fdf635ee897197fbe\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b2472f50da5cefff325a08ff4ea7fb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96393347707ed350827728d1fdb5a6c1\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7ff2d001e56166534f7627423f6c3dc\transformed\media-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5913ac8159fc2aef4dfef85fc61fd674\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91e034951279e3ef24a118c85d9e8639\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46ea3a9738606828198a06da3106bf28\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16f6cc94df42a04331333d9a929e8f1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0adab566f77761fdabd0a708d36f30f6\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:17:1-37:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e0459edfe9b39b6e98c60a6648a1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a41f38d22d3cd970475945165a3d39b5\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [:expo-manifests] D:\Freelance\MotelManagement\client\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-json-utils] D:\Freelance\MotelManagement\client\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [:expo-updates-interface] D:\Freelance\MotelManagement\client\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57f54075348123f87e67a9bc67fa90fb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b45743d29ca35c7388b5beb781b7e948\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cf56301e22695e89c32549beaa2d130\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9428404b5bdeff8a197a0bf484f208a\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5b97fbb617a90de4801b4528a1022283\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\baf9b8ccafc8bef9dfab9bcc80a987bd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1636afba8ed6db655f1261199ba2756d\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ca2068c065313f42401f503395cbff1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cefe0520af63b5108de46aaf60acb9ba\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\231931a7aa5c564494bca26834fb4750\transformed\core-runtime-2.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cbe3d6fd84f118b130262c1de2b868\transformed\sqlite-2.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac8150c032b7a8984bcb476bcf2d56f1\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df92957d989e74969c471a184e1acfd1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec91eead8be826d1b60978c64ac9903\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a4892e0e61a0218cb3409e3308d6bdb9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\166688ab8b0217e0237c6a3517571028\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:2:1-7:12
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:2:1-17:12
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\7ee1972708ed6497ae9c283ecbde7bf6\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:1:1-3:12
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:2:1-13:12
	package
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:2:5-51
	android:versionCode
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:1:11-69
uses-permission#android.permission.CAMERA
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:2:3-62
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:2:20-60
uses-permission#android.permission.INTERNET
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:3:3-64
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:13:5-67
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:3:20-62
uses-permission#android.permission.MEDIA_LIBRARY
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:4:3-69
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:4:20-67
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:5:3-77
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-80
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:5-80
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:5:20-75
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:3-75
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:5-78
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:16:5-78
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:20-73
uses-permission#android.permission.VIBRATE
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:7:3-63
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:7:20-61
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:8:3-78
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-81
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-81
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-81
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:8:20-76
queries
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:9:3-15:13
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:5-18:15
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-15:15
MERGED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:15
MERGED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-13:15
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:10:5-14:14
action#android.intent.action.VIEW
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:7-58
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:15-56
category#android.intent.category.BROWSABLE
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:7-67
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:17-65
data
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:7-37
	android:scheme
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:13-35
application
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:3-36:17
MERGED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:3-36:17
MERGED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:3-36:17
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:5-162
MERGED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-17:19
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:5-31:19
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:17:5-27:19
MERGED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-16:19
MERGED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-16:19
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:18:5-22:19
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\AndroidManifest.xml:24:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\52963e56ca573c7c394dc0939c7e7189\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\52963e56ca573c7c394dc0939c7e7189\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:11:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:25:5-35:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:24:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\baf9b8ccafc8bef9dfab9bcc80a987bd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\baf9b8ccafc8bef9dfab9bcc80a987bd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:11:5-15:19
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:11:5-20
	android:extractNativeLibs
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:221-264
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:221-264
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:48-80
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:48-80
	tools:ignore
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:75-114
	android:roundIcon
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:116-161
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:116-161
	tools:targetApi
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:54-74
	android:icon
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:81-115
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:81-115
	android:allowBackup
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:162-188
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:162-188
	android:theme
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:189-220
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:189-220
	tools:replace
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:115-159
	android:usesCleartextTraffic
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:18-53
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:16-47
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:16-47
meta-data#expo.modules.updates.ENABLED
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:5-82
	android:value
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:60-80
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:16-59
meta-data#expo.modules.updates.EXPO_RUNTIME_VERSION
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:5-119
	android:value
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:73-117
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:16-72
meta-data#expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:5-105
	android:value
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:81-103
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:16-80
meta-data#expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:5-99
	android:value
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:80-97
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:16-79
meta-data#expo.modules.updates.EXPO_UPDATE_URL
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:5-141
	android:value
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:68-139
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:16-67
activity#com.datacoolie.quanlynhatro.MainActivity
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:5-34:16
	android:screenOrientation
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:280-316
	android:launchMode
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:135-166
	android:windowSoftInputMode
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:167-209
	android:exported
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:256-279
	android:configChanges
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:44-134
	android:theme
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:210-255
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:15-43
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:23:7-26:23
action#android.intent.action.MAIN
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:24:9-60
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:24:17-58
category#android.intent.category.LAUNCHER
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:25:9-68
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:25:19-66
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:scheme:com.datacoolie.quanlynhatro+data:scheme:com.datacoolie.quanlynhatro
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:27:7-33:23
category#android.intent.category.DEFAULT
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:29:9-67
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:29:19-65
activity#com.facebook.react.devsupport.DevSettingsActivity
ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:5-106
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:19:9-21:40
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:19:9-21:40
	android:exported
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:80-104
	android:name
		ADDED from D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:15-79
uses-sdk
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\a870a614a2a06c3333f98bd77b21ae23\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:flipper-integration:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\a870a614a2a06c3333f98bd77b21ae23\transformed\jetified-flipper-integration-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\Freelance\MotelManagement\client\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-async-storage_async-storage] D:\Freelance\MotelManagement\client\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] D:\Freelance\MotelManagement\client\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-masked-view_masked-view] D:\Freelance\MotelManagement\client\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\Freelance\MotelManagement\client\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo] D:\Freelance\MotelManagement\client\node_modules\expo\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Freelance\MotelManagement\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-gesture-handler] D:\Freelance\MotelManagement\client\node_modules\react-native-gesture-handler\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Freelance\MotelManagement\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-reanimated] D:\Freelance\MotelManagement\client\node_modules\react-native-reanimated\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Freelance\MotelManagement\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-safe-area-context] D:\Freelance\MotelManagement\client\node_modules\react-native-safe-area-context\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\Freelance\MotelManagement\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-screens] D:\Freelance\MotelManagement\client\node_modules\react-native-screens\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Freelance\MotelManagement\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-svg] D:\Freelance\MotelManagement\client\node_modules\react-native-svg\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\Freelance\MotelManagement\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-vector-icons] D:\Freelance\MotelManagement\client\node_modules\react-native-vector-icons\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] D:\Freelance\MotelManagement\client\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-view-shot] D:\Freelance\MotelManagement\client\node_modules\react-native-view-shot\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] D:\Freelance\MotelManagement\client\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-application] D:\Freelance\MotelManagement\client\node_modules\expo-application\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\Freelance\MotelManagement\client\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-constants] D:\Freelance\MotelManagement\client\node_modules\expo-constants\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] D:\Freelance\MotelManagement\client\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-crypto] D:\Freelance\MotelManagement\client\node_modules\expo-crypto\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] D:\Freelance\MotelManagement\client\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-eas-client] D:\Freelance\MotelManagement\client\node_modules\expo-eas-client\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-font] D:\Freelance\MotelManagement\client\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-font] D:\Freelance\MotelManagement\client\node_modules\expo-font\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] D:\Freelance\MotelManagement\client\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-keep-awake] D:\Freelance\MotelManagement\client\node_modules\expo-keep-awake\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] D:\Freelance\MotelManagement\client\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-linear-gradient] D:\Freelance\MotelManagement\client\node_modules\expo-linear-gradient\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] D:\Freelance\MotelManagement\client\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-structured-headers] D:\Freelance\MotelManagement\client\node_modules\expo-structured-headers\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:6:5-44
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.react:react-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\AndroidManifest.xml:10:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e36c40fee784602eb08396fe737bc10e\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper-fresco-plugin:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\e36c40fee784602eb08396fe737bc10e\transformed\jetified-flipper-fresco-plugin-3.1.3\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9952f23c7889cc5aea17b03fb07e26b7\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:flipper:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9952f23c7889cc5aea17b03fb07e26b7\transformed\jetified-flipper-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9c51337f957f47544c05cf8cf4133b64\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fresco:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9c51337f957f47544c05cf8cf4133b64\transformed\jetified-fresco-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\4b0b4cd2d24d45c8c082cb3d9374c831\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-okhttp3:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\4b0b4cd2d24d45c8c082cb3d9374c831\transformed\jetified-imagepipeline-okhttp3-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6841ca629dd99e9fcf6417289e24d\transformed\jetified-animated-gif-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-gif:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f4f6841ca629dd99e9fcf6417289e24d\transformed\jetified-animated-gif-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ea510f873b60a34b61bac156c95e5df1\transformed\jetified-animated-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ea510f873b60a34b61bac156c95e5df1\transformed\jetified-animated-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\20a3485050f8121eb7feba396a2800a0\transformed\jetified-webpsupport-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:webpsupport:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\20a3485050f8121eb7feba396a2800a0\transformed\jetified-webpsupport-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9ebe7b5dc7b8de6d5417c17fba38a28a\transformed\jetified-animated-drawable-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:animated-drawable:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\9ebe7b5dc7b8de6d5417c17fba38a28a\transformed\jetified-animated-drawable-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\62406b7471376d629a0a99248577b65e\transformed\jetified-vito-options-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-options:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\62406b7471376d629a0a99248577b65e\transformed\jetified-vito-options-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ff0d475921ea02f1e153871151fa3385\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:drawee:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ff0d475921ea02f1e153871151fa3385\transformed\jetified-drawee-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b2e5dc2bb6c4ae24ba71a02a8b6f8a19\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagefilters:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b2e5dc2bb6c4ae24ba71a02a8b6f8a19\transformed\jetified-nativeimagefilters-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8882593734494990e8bbfd9a03a7c16a\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\8882593734494990e8bbfd9a03a7c16a\transformed\jetified-memory-type-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\13482a7c3fc1f087110086c4f267b08c\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-java:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\13482a7c3fc1f087110086c4f267b08c\transformed\jetified-memory-type-java-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\bcb653cf915589d42f0265d9b26dd2ea\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-native:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\bcb653cf915589d42f0265d9b26dd2ea\transformed\jetified-imagepipeline-native-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1842d5aaf193c3590824e8671e646f0\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:memory-type-ashmem:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\c1842d5aaf193c3590824e8671e646f0\transformed\jetified-memory-type-ashmem-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\016c018e8cbf9f5e043d8943831595be\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\016c018e8cbf9f5e043d8943831595be\transformed\jetified-imagepipeline-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee74f8dcbcd920820baee2163479df9e\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:nativeimagetranscoder:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\ee74f8dcbcd920820baee2163479df9e\transformed\jetified-nativeimagetranscoder-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\26120840317a18bc9d9347cea1d73faa\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:imagepipeline-base:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\26120840317a18bc9d9347cea1d73faa\transformed\jetified-imagepipeline-base-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc396fa3573087d48bcd56df7ae0e564\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:middleware:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\dc396fa3573087d48bcd56df7ae0e564\transformed\jetified-middleware-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b0acc2ffa0da4a23d2d882a6a9162ace\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:ui-common:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\b0acc2ffa0da4a23d2d882a6a9162ace\transformed\jetified-ui-common-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f153dbd822e587e820117285090a48da\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:soloader:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\f153dbd822e587e820117285090a48da\transformed\jetified-soloader-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\650ae4f1204e322a49235413a6e2319a\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:fbcore:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\650ae4f1204e322a49235413a6e2319a\transformed\jetified-fbcore-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c9733fdc73e8ee7f4ca3ea81b9b7f38\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper-network-plugin:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\6c9733fdc73e8ee7f4ca3ea81b9b7f38\transformed\jetified-flipper-network-plugin-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:11:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6780a2009a4dd79695e6581211bbd520\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6780a2009a4dd79695e6581211bbd520\transformed\jetified-appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.material:material:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\52963e56ca573c7c394dc0939c7e7189\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.stetho:stetho:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\52963e56ca573c7c394dc0939c7e7189\transformed\jetified-stetho-1.6.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\965ca604c3431eab8811bd7682895e8c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\965ca604c3431eab8811bd7682895e8c\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a7124dc67ace9e9baf7b1abad3799cb\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0a7124dc67ace9e9baf7b1abad3799cb\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da42cbaf7703b72f4f3ec0f19030b2e7\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\da42cbaf7703b72f4f3ec0f19030b2e7\transformed\jetified-viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de17595a4d979f58fd1f601ac0799b91\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\de17595a4d979f58fd1f601ac0799b91\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd027cdca6a3814175c0e84b7a4c5d2e\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\cd027cdca6a3814175c0e84b7a4c5d2e\transformed\jetified-activity-1.6.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d321e0369755bc3b82a1b538c01f9359\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\d321e0369755bc3b82a1b538c01f9359\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8364e952541680155bc059ebe3ea4e98\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8364e952541680155bc059ebe3ea4e98\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feb21053db54873509bb960ea0c337c4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feb21053db54873509bb960ea0c337c4\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17d0b8de03eb9ef3719cb0506b2e21aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\17d0b8de03eb9ef3719cb0506b2e21aa\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\76961dff289650ce4988209cdd2f9f82\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\76961dff289650ce4988209cdd2f9f82\transformed\lifecycle-viewmodel-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f5fd5369f3ba90afba7fa67af778d07\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f5fd5369f3ba90afba7fa67af778d07\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [com.facebook.fresco:vito-renderer:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\691b24e7e424686398ace54a44caf63b\transformed\jetified-vito-renderer-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fresco:vito-renderer:3.1.3] C:\Users\<USER>\.gradle\caches\transforms-3\691b24e7e424686398ace54a44caf63b\transformed\jetified-vito-renderer-3.1.3\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\92dc0b6aadd0ed3b164a189f125eb83f\transformed\jetified-hermes-android-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.react:hermes-android:0.73.6] C:\Users\<USER>\.gradle\caches\transforms-3\92dc0b6aadd0ed3b164a189f125eb83f\transformed\jetified-hermes-android-0.73.6-debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4188461951c9f776c3d1550af145b752\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4188461951c9f776c3d1550af145b752\transformed\jetified-savedstate-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\196777c3327b052411eae2e80d777398\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\196777c3327b052411eae2e80d777398\transformed\jetified-autofill-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5340bca6a91b967163c8ef199ba62c17\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5340bca6a91b967163c8ef199ba62c17\transformed\swiperefreshlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a02fcc903b26d99f6b23a1948ee57ab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a02fcc903b26d99f6b23a1948ee57ab\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe41b4c2d62e7dd412db20eb18527d61\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\fe41b4c2d62e7dd412db20eb18527d61\transformed\webkit-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a160af3ac3ef548fc1eb1aec42f7a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8c2a160af3ac3ef548fc1eb1aec42f7a\transformed\jetified-emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1702ff7bcc4716f06294717707dd016f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\1702ff7bcc4716f06294717707dd016f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d335db4f737189c64a1581c0213a89b4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\d335db4f737189c64a1581c0213a89b4\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e831abd82e09fbed4cbdd896e31662\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.6.0] C:\Users\<USER>\.gradle\caches\transforms-3\46e831abd82e09fbed4cbdd896e31662\transformed\browser-1.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00951d9a7646795fdf635ee897197fbe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\00951d9a7646795fdf635ee897197fbe\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b2472f50da5cefff325a08ff4ea7fb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\7b2472f50da5cefff325a08ff4ea7fb4\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96393347707ed350827728d1fdb5a6c1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\96393347707ed350827728d1fdb5a6c1\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7ff2d001e56166534f7627423f6c3dc\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.media:media:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b7ff2d001e56166534f7627423f6c3dc\transformed\media-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5913ac8159fc2aef4dfef85fc61fd674\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5913ac8159fc2aef4dfef85fc61fd674\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91e034951279e3ef24a118c85d9e8639\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\91e034951279e3ef24a118c85d9e8639\transformed\slidingpanelayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46ea3a9738606828198a06da3106bf28\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\46ea3a9738606828198a06da3106bf28\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16f6cc94df42a04331333d9a929e8f1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c16f6cc94df42a04331333d9a929e8f1\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0adab566f77761fdabd0a708d36f30f6\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\0adab566f77761fdabd0a708d36f30f6\transformed\jetified-core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e0459edfe9b39b6e98c60a6648a1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e0459edfe9b39b6e98c60a6648a1649\transformed\jetified-tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a41f38d22d3cd970475945165a3d39b5\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing-ktx:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\a41f38d22d3cd970475945165a3d39b5\transformed\jetified-tracing-ktx-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [:expo-manifests] D:\Freelance\MotelManagement\client\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-manifests] D:\Freelance\MotelManagement\client\node_modules\expo-manifests\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\Freelance\MotelManagement\client\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-json-utils] D:\Freelance\MotelManagement\client\node_modules\expo-json-utils\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\Freelance\MotelManagement\client\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:expo-updates-interface] D:\Freelance\MotelManagement\client\node_modules\expo-updates-interface\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57f54075348123f87e67a9bc67fa90fb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57f54075348123f87e67a9bc67fa90fb\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b45743d29ca35c7388b5beb781b7e948\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b45743d29ca35c7388b5beb781b7e948\transformed\lifecycle-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cf56301e22695e89c32549beaa2d130\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cf56301e22695e89c32549beaa2d130\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9428404b5bdeff8a197a0bf484f208a\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite-framework:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9428404b5bdeff8a197a0bf484f208a\transformed\sqlite-framework-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5b97fbb617a90de4801b4528a1022283\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-3\5b97fbb617a90de4801b4528a1022283\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\baf9b8ccafc8bef9dfab9bcc80a987bd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\baf9b8ccafc8bef9dfab9bcc80a987bd\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1636afba8ed6db655f1261199ba2756d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1636afba8ed6db655f1261199ba2756d\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ca2068c065313f42401f503395cbff1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\6ca2068c065313f42401f503395cbff1\transformed\lifecycle-livedata-2.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cefe0520af63b5108de46aaf60acb9ba\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\cefe0520af63b5108de46aaf60acb9ba\transformed\lifecycle-livedata-core-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\231931a7aa5c564494bca26834fb4750\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\231931a7aa5c564494bca26834fb4750\transformed\core-runtime-2.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cbe3d6fd84f118b130262c1de2b868\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.sqlite:sqlite:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\03cbe3d6fd84f118b130262c1de2b868\transformed\sqlite-2.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac8150c032b7a8984bcb476bcf2d56f1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\ac8150c032b7a8984bcb476bcf2d56f1\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df92957d989e74969c471a184e1acfd1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\df92957d989e74969c471a184e1acfd1\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec91eead8be826d1b60978c64ac9903\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\aec91eead8be826d1b60978c64ac9903\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a4892e0e61a0218cb3409e3308d6bdb9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\a4892e0e61a0218cb3409e3308d6bdb9\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\166688ab8b0217e0237c6a3517571028\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.fbjni:fbjni:0.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\166688ab8b0217e0237c6a3517571028\transformed\jetified-fbjni-0.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:7:5-9:41
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\7ee1972708ed6497ae9c283ecbde7bf6\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.ndk.thirdparty:openssl:1.1.1l-beta-1] C:\Users\<USER>\.gradle\caches\transforms-3\7ee1972708ed6497ae9c283ecbde7bf6\transformed\jetified-openssl-1.1.1l-beta-1\AndroidManifest.xml:2:2-70
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:5:5-7:41
	android:targetSdkVersion
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml
provider#com.reactnativecommunity.webview.RNCWebViewFileProvider
ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
	android:grantUriPermissions
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
	android:authorities
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
	android:exported
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
	android:name
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-83
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
	android:resource
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
	android:name
		ADDED from [:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
	android:name
		ADDED from [:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
intent#action:name:android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-17:18
action#android.intent.action.OPEN_DOCUMENT_TREE
ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-79
	android:name
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:21-76
provider#expo.modules.filesystem.FileSystemFileProvider
ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-30:20
	android:grantUriPermissions
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-47
	android:authorities
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
	android:exported
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
	tools:replace
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:26:13-48
	android:name
		ADDED from [:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-74
uses-permission#android.permission.READ_MEDIA_IMAGES
ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-76
	android:name
		ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-73
uses-permission#android.permission.READ_MEDIA_VIDEO
ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-75
	android:name
		ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-72
uses-permission#android.permission.READ_MEDIA_AUDIO
ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-75
	android:name
		ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-72
uses-permission#android.permission.ACCESS_MEDIA_LOCATION
ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
	android:name
		ADDED from [:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-77
intent#action:name:android.intent.action.SEND+data:mimeType:*/*
ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-14:18
action#android.intent.action.SEND
ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-65
	android:name
		ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-62
provider#expo.modules.sharing.SharingFileProvider
ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-26:20
	android:grantUriPermissions
		ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-47
	android:authorities
		ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-71
	android:exported
		ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-37
	android:name
		ADDED from [:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-68
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
	android:name
		ADDED from [:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
meta-data#org.unimodules.core.AppLoader#react-native-headless
ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-11:89
	android:value
		ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-86
	android:name
		ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-79
meta-data#com.facebook.soloader.enabled
ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-15:45
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
MERGED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:12:9-14:37
	tools:replace
		ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:13-42
	android:value
		ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
		REJECTED from [com.facebook.soloader:soloader:0.10.5] C:\Users\<USER>\.gradle\caches\transforms-3\f12a0b4f76d40a018c0e0e2a0b997ab0\transformed\jetified-soloader-0.10.5\AndroidManifest.xml:14:13-34
	android:name
		ADDED from [:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-57
uses-permission#android.permission.ACCESS_WIFI_STATE
ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
	android:name
		ADDED from [com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:26:9-34:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.datacoolie.quanlynhatro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.datacoolie.quanlynhatro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:25:9-28:40
	android:exported
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:28:13-37
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:27:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:26:13-74
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
	android:name
		ADDED from [com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:22-107
