1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.datacoolie.quanlynhatro"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:3-75
11-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:6:20-73
12    <uses-permission android:name="android.permission.CAMERA" />
12-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:2:3-62
12-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:2:20-60
13    <uses-permission android:name="android.permission.INTERNET" />
13-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:3:3-64
13-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:3:20-62
14    <uses-permission android:name="android.permission.MEDIA_LIBRARY" />
14-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:4:3-69
14-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:4:20-67
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:5:3-77
15-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:5:20-75
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:7:3-63
16-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:7:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:8:3-78
17-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:8:20-76
18
19    <queries>
19-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:9:3-15:13
20        <intent>
20-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:10:5-14:14
21            <action android:name="android.intent.action.VIEW" />
21-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:7-58
21-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:7-67
23-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:17-65
24
25            <data android:scheme="https" />
25-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:7-37
25-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-14:18
32
33            <!-- Required for file sharing if targeting API 30 -->
34            <action android:name="android.intent.action.SEND" />
34-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-65
34-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-62
35
36            <data android:mimeType="*/*" />
36-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:7-37
37        </intent>
38        <intent>
38-->[:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:18
39
40            <!-- Required for opening tabs if targeting API 30 -->
41            <action android:name="android.support.customtabs.action.CustomTabsService" />
41-->[:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-90
41-->[:expo-web-browser] D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:21-87
42        </intent>
43    </queries>
44
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->[:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-79
45-->[:expo-updates] D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-76
46    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
46-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:5-76
46-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:7:22-73
47    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
47-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:5-75
47-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:22-72
48    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
48-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:5-75
48-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:22-72
49    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
49-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:5-80
49-->[:expo-media-library] D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:22-77
50    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
50-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:5-76
50-->[com.facebook.flipper:flipper:0.201.0] C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\AndroidManifest.xml:14:22-73
51
52    <permission
52-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
53        android:name="com.datacoolie.quanlynhatro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
53-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
54        android:protectionLevel="signature" />
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
55
56    <uses-permission android:name="com.datacoolie.quanlynhatro.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
56-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
57    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
57-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:5-110
57-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\transforms-3\5916bf72a5c0d2cbbd79c680313cc06e\transformed\jetified-installreferrer-2.2\AndroidManifest.xml:9:22-107
58
59    <application
59-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:3-36:17
60        android:name="com.datacoolie.quanlynhatro.MainApplication"
60-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:16-47
61        android:allowBackup="true"
61-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:162-188
62        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
62-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
63        android:debuggable="true"
64        android:extractNativeLibs="false"
65        android:icon="@mipmap/ic_launcher"
65-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:81-115
66        android:label="@string/app_name"
66-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:48-80
67        android:requestLegacyExternalStorage="true"
67-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:221-264
68        android:roundIcon="@mipmap/ic_launcher_round"
68-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:116-161
69        android:theme="@style/AppTheme"
69-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:16:189-220
70        android:usesCleartextTraffic="true" >
70-->D:\Freelance\MotelManagement\client\android\app\src\debug\AndroidManifest.xml:6:18-53
71        <meta-data
71-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:5-82
72            android:name="expo.modules.updates.ENABLED"
72-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:16-59
73            android:value="true" />
73-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:17:60-80
74        <meta-data
74-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:5-119
75            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
75-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:16-72
76            android:value="@string/expo_runtime_version" />
76-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:18:73-117
77        <meta-data
77-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:5-105
78            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
78-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:16-80
79            android:value="ALWAYS" />
79-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:19:81-103
80        <meta-data
80-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:5-99
81            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
81-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:16-79
82            android:value="0" />
82-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:20:80-97
83        <meta-data
83-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:5-141
84            android:name="expo.modules.updates.EXPO_UPDATE_URL"
84-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:16-67
85            android:value="https://u.expo.dev/d96385e0-045f-4eda-975e-b192ca5ec3ca" />
85-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:21:68-139
86
87        <activity
87-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:5-34:16
88            android:name="com.datacoolie.quanlynhatro.MainActivity"
88-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:15-43
89            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
89-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:44-134
90            android:exported="true"
90-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:256-279
91            android:launchMode="singleTask"
91-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:135-166
92            android:screenOrientation="portrait"
92-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:280-316
93            android:theme="@style/Theme.App.SplashScreen"
93-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:210-255
94            android:windowSoftInputMode="adjustResize" >
94-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:22:167-209
95            <intent-filter>
95-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:23:7-26:23
96                <action android:name="android.intent.action.MAIN" />
96-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:24:9-60
96-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:24:17-58
97
98                <category android:name="android.intent.category.LAUNCHER" />
98-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:25:9-68
98-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:25:19-66
99            </intent-filter>
100            <intent-filter>
100-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:27:7-33:23
101                <action android:name="android.intent.action.VIEW" />
101-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:7-58
101-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:11:15-56
102
103                <category android:name="android.intent.category.DEFAULT" />
103-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:29:9-67
103-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:29:19-65
104                <category android:name="android.intent.category.BROWSABLE" />
104-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:7-67
104-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:12:17-65
105
106                <data android:scheme="com.datacoolie.quanlynhatro" />
106-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:7-37
106-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:13-35
107                <data android:scheme="com.datacoolie.quanlynhatro" />
107-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:7-37
107-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:13:13-35
108            </intent-filter>
109        </activity>
110        <activity
110-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:5-106
111            android:name="com.facebook.react.devsupport.DevSettingsActivity"
111-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:15-79
112            android:exported="false" />
112-->D:\Freelance\MotelManagement\client\android\app\src\main\AndroidManifest.xml:35:80-104
113
114        <provider
114-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-16:20
115            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
115-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-83
116            android:authorities="com.datacoolie.quanlynhatro.fileprovider"
116-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-64
117            android:exported="false"
117-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-37
118            android:grantUriPermissions="true" >
118-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-47
119            <meta-data
119-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
120                android:name="android.support.FILE_PROVIDER_PATHS"
120-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
121                android:resource="@xml/file_provider_paths" />
121-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
122        </provider>
123        <provider
123-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:9-30:20
124            android:name="expo.modules.filesystem.FileSystemFileProvider"
124-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-74
125            android:authorities="com.datacoolie.quanlynhatro.FileSystemFileProvider"
125-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:23:13-74
126            android:exported="false"
126-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-37
127            android:grantUriPermissions="true" >
127-->[:expo-file-system] D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:25:13-47
128            <meta-data
128-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
129                android:name="android.support.FILE_PROVIDER_PATHS"
129-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
130                android:resource="@xml/file_system_provider_paths" />
130-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
131        </provider>
132        <provider
132-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:18:9-26:20
133            android:name="expo.modules.sharing.SharingFileProvider"
133-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:19:13-68
134            android:authorities="com.datacoolie.quanlynhatro.SharingFileProvider"
134-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-71
135            android:exported="false"
135-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-37
136            android:grantUriPermissions="true" >
136-->[:expo-sharing] D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-47
137            <meta-data
137-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-15:63
138                android:name="android.support.FILE_PROVIDER_PATHS"
138-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:17-67
139                android:resource="@xml/sharing_provider_paths" />
139-->[:react-native-webview] D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-60
140        </provider>
141
142        <meta-data
142-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-11:89
143            android:name="org.unimodules.core.AppLoader#react-native-headless"
143-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-79
144            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
144-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-86
145        <meta-data
145-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:12:9-15:45
146            android:name="com.facebook.soloader.enabled"
146-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-57
147            android:value="true" />
147-->[:expo-modules-core] D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-33
148
149        <provider
149-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
150            android:name="androidx.startup.InitializationProvider"
150-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
151            android:authorities="com.datacoolie.quanlynhatro.androidx-startup"
151-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
152            android:exported="false" >
152-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
153            <meta-data
153-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
154                android:name="androidx.emoji2.text.EmojiCompatInitializer"
154-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
155                android:value="androidx.startup" />
155-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
156            <meta-data
156-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
157                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
157-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
158                android:value="androidx.startup" />
158-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
159        </provider>
160
161        <service
161-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:25:9-28:40
162            android:name="androidx.room.MultiInstanceInvalidationService"
162-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:26:13-74
163            android:directBootAware="true"
163-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:27:13-43
164            android:exported="false" />
164-->[androidx.room:room-runtime:2.4.2] C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\AndroidManifest.xml:28:13-37
165    </application>
166
167</manifest>
