com.datacoolie.quanlynhatro.app-sqlite-2.2.0-0 C:\Users\<USER>\.gradle\caches\transforms-3\03cbe3d6fd84f118b130262c1de2b868\transformed\sqlite-2.2.0\res
com.datacoolie.quanlynhatro.app-jetified-core-ktx-1.12.0-1 C:\Users\<USER>\.gradle\caches\transforms-3\0adab566f77761fdabd0a708d36f30f6\transformed\jetified-core-ktx-1.12.0\res
com.datacoolie.quanlynhatro.app-room-runtime-2.4.2-2 C:\Users\<USER>\.gradle\caches\transforms-3\11e97b619513970748d6de6b7cb5e8f4\transformed\room-runtime-2.4.2\res
com.datacoolie.quanlynhatro.app-cardview-1.0.0-3 C:\Users\<USER>\.gradle\caches\transforms-3\1636afba8ed6db655f1261199ba2756d\transformed\cardview-1.0.0\res
com.datacoolie.quanlynhatro.app-jetified-autofill-1.1.0-4 C:\Users\<USER>\.gradle\caches\transforms-3\196777c3327b052411eae2e80d777398\transformed\jetified-autofill-1.1.0\res
com.datacoolie.quanlynhatro.app-jetified-startup-runtime-1.1.1-5 C:\Users\<USER>\.gradle\caches\transforms-3\1c4ad1c0a2c51e307bb85a2a12a5bd8e\transformed\jetified-startup-runtime-1.1.1\res
com.datacoolie.quanlynhatro.app-coordinatorlayout-1.2.0-6 C:\Users\<USER>\.gradle\caches\transforms-3\2a02fcc903b26d99f6b23a1948ee57ab\transformed\coordinatorlayout-1.2.0\res
com.datacoolie.quanlynhatro.app-jetified-tracing-1.2.0-7 C:\Users\<USER>\.gradle\caches\transforms-3\2e0459edfe9b39b6e98c60a6648a1649\transformed\jetified-tracing-1.2.0\res
com.datacoolie.quanlynhatro.app-jetified-lifecycle-viewmodel-savedstate-2.5.1-8 C:\Users\<USER>\.gradle\caches\transforms-3\3f5fd5369f3ba90afba7fa67af778d07\transformed\jetified-lifecycle-viewmodel-savedstate-2.5.1\res
com.datacoolie.quanlynhatro.app-jetified-savedstate-1.2.0-9 C:\Users\<USER>\.gradle\caches\transforms-3\4188461951c9f776c3d1550af145b752\transformed\jetified-savedstate-1.2.0\res
com.datacoolie.quanlynhatro.app-browser-1.6.0-10 C:\Users\<USER>\.gradle\caches\transforms-3\46e831abd82e09fbed4cbdd896e31662\transformed\browser-1.6.0\res
com.datacoolie.quanlynhatro.app-jetified-emoji2-1.2.0-11 C:\Users\<USER>\.gradle\caches\transforms-3\4e292a0dbf38262aded1b9d63d414de2\transformed\jetified-emoji2-1.2.0\res
com.datacoolie.quanlynhatro.app-swiperefreshlayout-1.1.0-12 C:\Users\<USER>\.gradle\caches\transforms-3\5340bca6a91b967163c8ef199ba62c17\transformed\swiperefreshlayout-1.1.0\res
com.datacoolie.quanlynhatro.app-recyclerview-1.1.0-13 C:\Users\<USER>\.gradle\caches\transforms-3\5913ac8159fc2aef4dfef85fc61fd674\transformed\recyclerview-1.1.0\res
com.datacoolie.quanlynhatro.app-jetified-appcompat-resources-1.6.1-14 C:\Users\<USER>\.gradle\caches\transforms-3\6780a2009a4dd79695e6581211bbd520\transformed\jetified-appcompat-resources-1.6.1\res
com.datacoolie.quanlynhatro.app-core-1.12.0-15 C:\Users\<USER>\.gradle\caches\transforms-3\6fa08ebee00822939d86a47ace4a1202\transformed\core-1.12.0\res
com.datacoolie.quanlynhatro.app-lifecycle-viewmodel-2.5.1-16 C:\Users\<USER>\.gradle\caches\transforms-3\76961dff289650ce4988209cdd2f9f82\transformed\lifecycle-viewmodel-2.5.1\res
com.datacoolie.quanlynhatro.app-drawerlayout-1.1.1-17 C:\Users\<USER>\.gradle\caches\transforms-3\7b2472f50da5cefff325a08ff4ea7fb4\transformed\drawerlayout-1.1.1\res
com.datacoolie.quanlynhatro.app-jetified-flipper-0.201.0-18 C:\Users\<USER>\.gradle\caches\transforms-3\8a2c25120e18a90bd0c7b612e5019e69\transformed\jetified-flipper-0.201.0\res
com.datacoolie.quanlynhatro.app-jetified-emoji2-views-helper-1.2.0-19 C:\Users\<USER>\.gradle\caches\transforms-3\8c2a160af3ac3ef548fc1eb1aec42f7a\transformed\jetified-emoji2-views-helper-1.2.0\res
com.datacoolie.quanlynhatro.app-transition-1.2.0-20 C:\Users\<USER>\.gradle\caches\transforms-3\96393347707ed350827728d1fdb5a6c1\transformed\transition-1.2.0\res
com.datacoolie.quanlynhatro.app-appcompat-1.6.1-21 C:\Users\<USER>\.gradle\caches\transforms-3\965ca604c3431eab8811bd7682895e8c\transformed\appcompat-1.6.1\res
com.datacoolie.quanlynhatro.app-jetified-tracing-ktx-1.2.0-22 C:\Users\<USER>\.gradle\caches\transforms-3\a41f38d22d3cd970475945165a3d39b5\transformed\jetified-tracing-ktx-1.2.0\res
com.datacoolie.quanlynhatro.app-jetified-annotation-experimental-1.3.0-23 C:\Users\<USER>\.gradle\caches\transforms-3\a4892e0e61a0218cb3409e3308d6bdb9\transformed\jetified-annotation-experimental-1.3.0\res
com.datacoolie.quanlynhatro.app-lifecycle-runtime-2.5.1-24 C:\Users\<USER>\.gradle\caches\transforms-3\b45743d29ca35c7388b5beb781b7e948\transformed\lifecycle-runtime-2.5.1\res
com.datacoolie.quanlynhatro.app-jetified-lifecycle-process-2.4.1-25 C:\Users\<USER>\.gradle\caches\transforms-3\b5a0a7b3c62090f5ec4385d6a79fee16\transformed\jetified-lifecycle-process-2.4.1\res
com.datacoolie.quanlynhatro.app-media-1.0.0-26 C:\Users\<USER>\.gradle\caches\transforms-3\b7ff2d001e56166534f7627423f6c3dc\transformed\media-1.0.0\res
com.datacoolie.quanlynhatro.app-jetified-activity-1.6.0-27 C:\Users\<USER>\.gradle\caches\transforms-3\cd027cdca6a3814175c0e84b7a4c5d2e\transformed\jetified-activity-1.6.0\res
com.datacoolie.quanlynhatro.app-lifecycle-livedata-core-2.5.1-28 C:\Users\<USER>\.gradle\caches\transforms-3\cefe0520af63b5108de46aaf60acb9ba\transformed\lifecycle-livedata-core-2.5.1\res
com.datacoolie.quanlynhatro.app-jetified-viewpager2-1.0.0-29 C:\Users\<USER>\.gradle\caches\transforms-3\da42cbaf7703b72f4f3ec0f19030b2e7\transformed\jetified-viewpager2-1.0.0\res
com.datacoolie.quanlynhatro.app-jetified-react-android-0.73.6-debug-30 C:\Users\<USER>\.gradle\caches\transforms-3\db3cb4c010cd7242b65007b624d40152\transformed\jetified-react-android-0.73.6-debug\res
com.datacoolie.quanlynhatro.app-fragment-1.3.6-31 C:\Users\<USER>\.gradle\caches\transforms-3\de17595a4d979f58fd1f601ac0799b91\transformed\fragment-1.3.6\res
com.datacoolie.quanlynhatro.app-sqlite-framework-2.2.0-32 C:\Users\<USER>\.gradle\caches\transforms-3\e9428404b5bdeff8a197a0bf484f208a\transformed\sqlite-framework-2.2.0\res
com.datacoolie.quanlynhatro.app-material-1.9.0-33 C:\Users\<USER>\.gradle\caches\transforms-3\f1852cac82263a0db8af6cf304099aeb\transformed\material-1.9.0\res
com.datacoolie.quanlynhatro.app-constraintlayout-2.0.1-34 C:\Users\<USER>\.gradle\caches\transforms-3\fbce3ea05e23c43d4828664bb6f57a15\transformed\constraintlayout-2.0.1\res
com.datacoolie.quanlynhatro.app-jetified-drawee-3.1.3-35 C:\Users\<USER>\.gradle\caches\transforms-3\ff0d475921ea02f1e153871151fa3385\transformed\jetified-drawee-3.1.3\res
com.datacoolie.quanlynhatro.app-google-services-36 D:\Freelance\MotelManagement\client\android\app\build\generated\res\google-services\debug
com.datacoolie.quanlynhatro.app-pngs-37 D:\Freelance\MotelManagement\client\android\app\build\generated\res\pngs\debug
com.datacoolie.quanlynhatro.app-resValues-38 D:\Freelance\MotelManagement\client\android\app\build\generated\res\resValues\debug
com.datacoolie.quanlynhatro.app-packageDebugResources-39 D:\Freelance\MotelManagement\client\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.datacoolie.quanlynhatro.app-packageDebugResources-40 D:\Freelance\MotelManagement\client\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.datacoolie.quanlynhatro.app-merged_res-41 D:\Freelance\MotelManagement\client\android\app\build\intermediates\merged_res\debug
com.datacoolie.quanlynhatro.app-debug-42 D:\Freelance\MotelManagement\client\android\app\src\debug\res
com.datacoolie.quanlynhatro.app-main-43 D:\Freelance\MotelManagement\client\android\app\src\main\res
com.datacoolie.quanlynhatro.app-packaged_res-44 D:\Freelance\MotelManagement\client\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-45 D:\Freelance\MotelManagement\client\node_modules\@react-native-masked-view\masked-view\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-46 D:\Freelance\MotelManagement\client\node_modules\expo-application\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-47 D:\Freelance\MotelManagement\client\node_modules\expo-constants\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-48 D:\Freelance\MotelManagement\client\node_modules\expo-crypto\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-49 D:\Freelance\MotelManagement\client\node_modules\expo-eas-client\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-50 D:\Freelance\MotelManagement\client\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-51 D:\Freelance\MotelManagement\client\node_modules\expo-font\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-52 D:\Freelance\MotelManagement\client\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-53 D:\Freelance\MotelManagement\client\node_modules\expo-keep-awake\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-54 D:\Freelance\MotelManagement\client\node_modules\expo-linear-gradient\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-55 D:\Freelance\MotelManagement\client\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-56 D:\Freelance\MotelManagement\client\node_modules\expo-media-library\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-57 D:\Freelance\MotelManagement\client\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-58 D:\Freelance\MotelManagement\client\node_modules\expo-sharing\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-59 D:\Freelance\MotelManagement\client\node_modules\expo-structured-headers\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-60 D:\Freelance\MotelManagement\client\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-61 D:\Freelance\MotelManagement\client\node_modules\expo-updates\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-62 D:\Freelance\MotelManagement\client\node_modules\expo-web-browser\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-63 D:\Freelance\MotelManagement\client\node_modules\expo\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-64 D:\Freelance\MotelManagement\client\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-65 D:\Freelance\MotelManagement\client\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-66 D:\Freelance\MotelManagement\client\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-67 D:\Freelance\MotelManagement\client\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-68 D:\Freelance\MotelManagement\client\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-69 D:\Freelance\MotelManagement\client\node_modules\react-native-vector-icons\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-70 D:\Freelance\MotelManagement\client\node_modules\react-native-view-shot\android\build\intermediates\packaged_res\debug
com.datacoolie.quanlynhatro.app-packaged_res-71 D:\Freelance\MotelManagement\client\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug
