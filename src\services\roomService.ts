import { collection, getDocs, query, where, onSnapshot, addDoc, serverTimestamp, doc, getDoc, updateDoc, DocumentReference, deleteDoc } from 'firebase/firestore';
import { db } from '../../config/firebase';
import { auth } from '../../config/firebase';
import { Room, Resident, HoaDon, ChiSoDienNuoc } from '../types';

// Collection references
const roomsRef = collection(db, 'rooms');
const residentsRef = collection(db, 'residents');
const hoaDonRef = collection(db, 'hoa_don');
const chiSoDienNuocRef = collection(db, 'chi_so_dien_nuoc');

// Add new room
export const addRoom = async (roomData: Omit<Room, 'room_id'>) => {
  try {
    if (!auth.currentUser) {
      throw new Error('User must be authenticated to create a room');
    }
    
    const docRef = await addDoc(roomsRef, {
      ...roomData,
      ownerId: auth.currentUser.uid,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Error adding room:', error);
    return { success: false, error };
  }
};

// Get all rooms for current user
export const getAllRooms = async (): Promise<Room[]> => {
  try {
    if (!auth.currentUser) {
      throw new Error('User must be authenticated to fetch rooms');
    }
    
    console.log('Fetching rooms for current user...');
    const q = query(roomsRef, where('ownerId', '==', auth.currentUser.uid));
    const snapshot = await getDocs(q);
    const rooms = snapshot.docs.map(doc => ({ ...doc.data(), room_id: doc.id } as Room));
    console.log('Fetched rooms:', rooms);
    return rooms;
  } catch (error) {
    console.error('Error fetching rooms:', error);
    return [];
  }
};

// Get a single room by ID
export const getRoomById = async (roomId: string): Promise<Room | null> => {
  try {
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));
    if (!roomDoc.exists()) {
      return null;
    }
    return { ...roomDoc.data(), room_id: roomDoc.id } as Room;
  } catch (error) {
    console.error('Error fetching room:', error);
    return null;
  }
};

// Update room details
export const updateRoom = async (roomId: string, roomData: Partial<Room>): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'rooms', roomId), {
      ...roomData,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error updating room:', error);
    return false;
  }
};

// Delete a room
export const deleteRoom = async (roomId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'rooms', roomId));
    return true;
  } catch (error) {
    console.error('Error deleting room:', error);
    return false;
  }
};

// Get residents by room ID
export const getResidentsByRoomId = async (roomId: string): Promise<Resident[]> => {
  console.log('Fetching residents for room:', roomId);  
  const q = query(residentsRef, where('room_id', '==', roomId));
  const snapshot = await getDocs(q);
  const residents = snapshot.docs.map(doc => {
    const data = doc.data();
    return {
      ...data,
      resident_id: doc.id,
      created_at: data.created_at?.toDate() || new Date(),
    } as Resident;
  });
  console.log('Fetched residents:', residents);
  return residents;
};

// Subscribe to rooms updates for current user
export const subscribeToRooms = (callback: (rooms: Room[]) => void) => {
  if (!auth.currentUser) {
    console.error('User must be authenticated to subscribe to rooms');
    return () => {};
  }

  console.log('Setting up rooms subscription for current user...');
  const q = query(roomsRef, where('ownerId', '==', auth.currentUser.uid));
  return onSnapshot(q, (snapshot) => {
    console.log('Received room update from Firebase');
    const rooms = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        room_id: doc.id,
        created_at: data.created_at?.toDate() || new Date(),
        start_date: data.start_date?.toDate() || null,
        end_date: data.end_date?.toDate() || null,
      } as Room;
    });
    console.log('Parsed rooms:', rooms);
    callback(rooms);
  }, (error) => {
    console.error('Error in rooms subscription:', error);
  });
};

// Subscribe to residents updates for a specific room
export const subscribeToRoomResidents = (roomId: string, callback: (residents: Resident[]) => void) => {  
  const q = query(residentsRef, where('room_id', '==', roomId));
  return onSnapshot(q, (snapshot) => {
    const residents = snapshot.docs.map(doc => {
      const data = doc.data();
      return {
        ...data,
        resident_id: doc.id,
        created_at: data.created_at?.toDate() || new Date(),
      } as Resident;
    });
    callback(residents);
  });
};

// Get bills for a room
export const getHoaDonByRoomId = async (roomId: string): Promise<HoaDon[]> => {
  try {
    const q = query(hoaDonRef, where('phong_id', '==', roomId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      ...doc.data(),
      hoadon_id: doc.id,
      created_at: doc.data().created_at?.toDate() || new Date(),
      due_date: doc.data().due_date?.toDate() || new Date(),
    }) as HoaDon);
  } catch (error) {
    console.error('Error fetching bills:', error);
    return [];
  }
};

// Subscribe to bills updates for a room
export const subscribeToRoomBills = (roomId: string, callback: (bills: HoaDon[]) => void) => {
  const q = query(hoaDonRef, where('phong_id', '==', roomId));
  return onSnapshot(q, (snapshot) => {
    const bills = snapshot.docs.map(doc => ({
      ...doc.data(),
      hoadon_id: doc.id,
      created_at: doc.data().created_at?.toDate() || new Date(),
      due_date: doc.data().due_date?.toDate() || new Date(),
    }) as HoaDon);
    callback(bills);
  });
};

// Update bill status
export const updateBillStatus = async (billId: string, status: HoaDon['payment_status']): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'hoa_don', billId), {
      payment_status: status,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error updating bill status:', error);
    return false;
  }
};

// Get utility readings for a room
export const getChiSoDienNuocByRoomId = async (roomId: string): Promise<ChiSoDienNuoc[]> => {
  try {
    const q = query(chiSoDienNuocRef, where('phong_id', '==', roomId));
    const snapshot = await getDocs(q);
    return snapshot.docs.map(doc => ({
      ...doc.data(),
      record_id: doc.id,
      created_at: doc.data().created_at?.toDate() || new Date(),
    }) as ChiSoDienNuoc);
  } catch (error) {
    console.error('Error fetching utility readings:', error);
    return [];
  }
};

// Add new residents
export const addResident = async (residentData: Omit<Resident, 'resident_id'>): Promise<string | null> => {
  try {
    const docRef = await addDoc(residentsRef, {
      ...residentData,
      created_at: serverTimestamp()
    });
    return docRef.id;
  } catch (error) {
    console.error('Error adding resident:', error);
    return null;
  }
};

// Update resident information
export const updateResident = async (residentId: string, residentData: Partial<Resident>): Promise<boolean> => {
  try {
    await updateDoc(doc(db, 'residents', residentId), {
      ...residentData,
      updatedAt: serverTimestamp()
    });
    return true;
  } catch (error) {
    console.error('Error updating resident:', error);
    return false;
  }
};

// Remove resident
export const removeResident = async (residentId: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'residents', residentId));
    return true;
  } catch (error) {
    console.error('Error removing resident:', error);
    return false;
  }
};