import {
  GoogleSignin,
  statusCodes,
} from "@react-native-google-signin/google-signin";
import { GoogleAuthProvider, signInWithCredential } from "firebase/auth";
import { auth } from "../../config/firebase";

// mock web client id
const WEB_CLIENT_ID = "************-placeholder.apps.googleusercontent.com";

export const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    webClientId: WEB_CLIENT_ID,
    offlineAccess: true,
    hostedDomain: "",
    forceCodeForRefreshToken: true,
    accountName: "",
    iosClientId: "",
    googleServicePlistPath: "",
    openIdSupport: false,
    profileImageSize: 120,
  });
};

export const signInWithGoogle = async () => {
  try {
    await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });

    const userInfo = await GoogleSignin.signIn();

    if (!userInfo.data?.idToken) {
      throw new Error("No ID token received from Google");
    }

    const googleCredential = GoogleAuthProvider.credential(
      userInfo.data.idToken
    );

    const userCredential = await signInWithCredential(auth, googleCredential);

    return {
      user: userCredential.user,
      additionalUserInfo: {
        isNewUser:
          userCredential.user.metadata.creationTime ===
          userCredential.user.metadata.lastSignInTime,
        profile: userInfo.data.user,
      },
    };
  } catch (error: any) {
    console.error("Google Sign-In Error:", error);

    if (error.code === statusCodes.SIGN_IN_CANCELLED) {
      throw new Error("SIGN_IN_CANCELLED");
    } else if (error.code === statusCodes.IN_PROGRESS) {
      throw new Error("SIGN_IN_IN_PROGRESS");
    } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
      throw new Error("PLAY_SERVICES_NOT_AVAILABLE");
    } else {
      throw new Error("GOOGLE_SIGN_IN_ERROR");
    }
  }
};
export const signOutFromGoogle = async () => {
  try {
    await GoogleSignin.signOut();
    await auth.signOut();
  } catch (error) {
    console.error("Google Sign-Out Error:", error);
    throw error;
  }
};

export const isSignedInToGoogle = async () => {
  try {
    return await GoogleSignin.isSignedIn();
  } catch (error) {
    console.error("Error checking Google sign-in status:", error);
    return false;
  }
};

export const getCurrentGoogleUser = async () => {
  try {
    const userInfo = await GoogleSignin.signInSilently();
    return userInfo;
  } catch (error) {
    console.error("Error getting current Google user:", error);
    return null;
  }
};

export const revokeGoogleAccess = async () => {
  try {
    await GoogleSignin.revokeAccess();
    await auth.signOut();
  } catch (error) {
    console.error("Error revoking Google access:", error);
    throw error;
  }
};

export const getGoogleSignInErrorMessage = (
  error: any,
  t: (key: string) => string
) => {
  switch (error.message) {
    case "SIGN_IN_CANCELLED":
      return t("auth.googleSignInCancelled");
    case "SIGN_IN_IN_PROGRESS":
      return t("auth.googleSignInInProgress");
    case "PLAY_SERVICES_NOT_AVAILABLE":
      return t("auth.playServicesNotAvailable");
    case "GOOGLE_SIGN_IN_ERROR":
    default:
      return t("auth.googleSignInError");
  }
};
