{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-az/values-az.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,258,361,465,566,671,782", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "151,253,356,460,561,666,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,114", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3440,3541,3643,3746,3850,3951,4056,9616", "endColumns": "100,101,102,103,100,104,110,100", "endOffsets": "3536,3638,3741,3845,3946,4051,4162,9712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,353,438,518,603,682,778,894,974,1038,1132,1200,1259,1354,1417,1481,1540,1607,1670,1724,1839,1897,1959,2013,2084,2216,2300,2380,2514,2590,2666,2750,2807,2858,2924,2994,3072,3155,3225,3301,3379,3450,3536,3619,3712,3805,3878,3950,4044,4098,4165,4249,4337,4401,4466,4530,4632,4729,4825,4922,4983,5038", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,83,56,50,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,60,54,79", "endOffsets": "260,348,433,513,598,677,773,889,969,1033,1127,1195,1254,1349,1412,1476,1535,1602,1665,1719,1834,1892,1954,2008,2079,2211,2295,2375,2509,2585,2661,2745,2802,2853,2919,2989,3067,3150,3220,3296,3374,3445,3531,3614,3707,3800,3873,3945,4039,4093,4160,4244,4332,4396,4461,4525,4627,4724,4820,4917,4978,5033,5113"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,50,51,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3023,3111,3196,3276,3361,4167,4263,4379,4636,4700,5109,5328,5455,5550,5613,5677,5736,5803,5866,5920,6035,6093,6155,6209,6280,6480,6564,6644,6778,6854,6930,7014,7071,7122,7188,7258,7336,7419,7489,7565,7643,7714,7800,7883,7976,8069,8142,8214,8308,8362,8429,8513,8601,8665,8730,8794,8896,8993,9089,9186,9247,9302", "endLines": "5,33,34,35,36,37,45,46,47,50,51,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,87,84,79,84,78,95,115,79,63,93,67,58,94,62,63,58,66,62,53,114,57,61,53,70,131,83,79,133,75,75,83,56,50,65,69,77,82,69,75,77,70,85,82,92,92,72,71,93,53,66,83,87,63,64,63,101,96,95,96,60,54,79", "endOffsets": "310,3106,3191,3271,3356,3435,4258,4374,4454,4695,4789,5172,5382,5545,5608,5672,5731,5798,5861,5915,6030,6088,6150,6204,6275,6407,6559,6639,6773,6849,6925,7009,7066,7117,7183,7253,7331,7414,7484,7560,7638,7709,7795,7878,7971,8064,8137,8209,8303,8357,8424,8508,8596,8660,8725,8789,8891,8988,9084,9181,9242,9297,9377"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,215,316,426,514,621,735,817,895,986,1079,1173,1272,1372,1465,1560,1654,1745,1837,1922,2027,2133,2233,2342,2447,2549,2707,2813", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "210,311,421,509,616,730,812,890,981,1074,1168,1267,1367,1460,1555,1649,1740,1832,1917,2022,2128,2228,2337,2442,2544,2702,2808,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,425,526,636,724,831,945,1027,1105,1196,1289,1383,1482,1582,1675,1770,1864,1955,2047,2132,2237,2343,2443,2552,2657,2759,2917,9382", "endColumns": "109,100,109,87,106,113,81,77,90,92,93,98,99,92,94,93,90,91,84,104,105,99,108,104,101,157,105,83", "endOffsets": "420,521,631,719,826,940,1022,1100,1191,1284,1378,1477,1577,1670,1765,1859,1950,2042,2127,2232,2338,2438,2547,2652,2754,2912,3018,9461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,255,363", "endColumns": "95,103,107,102", "endOffsets": "146,250,358,461"}, "to": {"startLines": "49,52,53,54", "startColumns": "4,4,4,4", "startOffsets": "4540,4794,4898,5006", "endColumns": "95,103,107,102", "endOffsets": "4631,4893,5001,5104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-az\\values-az.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "55,136,205,287,355,423,498", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "131,200,282,350,418,493,568"}, "to": {"startLines": "48,56,57,59,73,112,113", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "4459,5177,5246,5387,6412,9466,9541", "endColumns": "80,68,81,67,67,74,74", "endOffsets": "4535,5241,5323,5450,6475,9536,9611"}}]}]}