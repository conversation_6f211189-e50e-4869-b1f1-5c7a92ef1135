import React, { useState, useEffect } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Surface,
  IconButton,
  FAB,
  Chip,
  Divider,
  ActivityIndicator,
  Searchbar,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";
import { mockMotels, mockRooms } from "../../services/mockData";

interface ExtendedMotel {
  motel_id: string;
  owner_id: string;
  name: string;
  address: string;
  managers: string[];
  created_at: Date;
  // UI properties
  totalRooms: number;
  availableRooms: number;
  occupiedRooms: number;
  maintenanceRooms: number;
  status: "active" | "construction" | "maintenance";
  image: string;
}

export default function MotelsManagement() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const [motels, setMotels] = useState<ExtendedMotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredMotels, setFilteredMotels] = useState<ExtendedMotel[]>([]);

  useEffect(() => {
    setTimeout(() => {
      const processedMotels: ExtendedMotel[] = mockMotels.map((motel) => {
        const motelRooms = mockRooms.filter(
          (room) => room.motel_id === motel.motel_id
        );

        const availableRooms = motelRooms.filter(
          (room) => room.status === "available"
        ).length;
        const occupiedRooms = motelRooms.filter(
          (room) => room.status === "occupied"
        ).length;
        const maintenanceRooms = motelRooms.filter(
          (room) => room.status === "maintenance"
        ).length;

        const statuses = ["active", "construction", "maintenance"] as const;
        const randomStatus =
          statuses[Math.floor(Math.random() * statuses.length)];

        const randomId = Math.floor(Math.random() * 100) + 1;
        const image = `https://upload.wikimedia.org/wikipedia/commons/thumb/d/d4/The_Lauren_condo_Bethesda_MD_2021-12-12_10-11-55_1.jpg/1200px-The_Lauren_condo_Bethesda_MD_2021-12-12_10-11-55_1.jpg`;

        return {
          ...motel,
          totalRooms: motelRooms.length,
          availableRooms,
          occupiedRooms,
          maintenanceRooms,
          status: randomStatus,
          image,
        };
      });

      const additionalMotels: ExtendedMotel[] = [
        {
          motel_id: "motel2",
          owner_id: "user1",
          name: "Nhà trọ Đà Lạt",
          address: "Chợ Đà Lạt, Đà Lạt, Lâm Đồng",
          managers: ["user2"],
          created_at: new Date("2023-03-15"),
          totalRooms: 12,
          availableRooms: 3,
          occupiedRooms: 8,
          maintenanceRooms: 1,
          status: "active",
          image:
            "https://cf.bstatic.com/static/img/theme-index/bg_motels/d856398f47116cf15b08e830bfd63530539cd8e1.jpg",
        },
        {
          motel_id: "motel3",
          owner_id: "user1",
          name: "Dãy B nhà trọ An",
          address: "100 Phạm Văn Đồng, Quận 1, TP.HCM",
          managers: [],
          created_at: new Date("2023-05-20"),
          totalRooms: 8,
          availableRooms: 0,
          occupiedRooms: 0,
          maintenanceRooms: 8,
          status: "construction",
          image:
            "https://images.trvl-media.com/lodging/74000000/73400000/73398900/73398847/27e93211.jpg?impolicy=fcrop&w=357&h=201&p=1&q=medium",
        },
        {
          motel_id: "motel4",
          owner_id: "user1",
          name: "Dãy A nhà trọ An",
          address: "100 Phạm Văn Đồng, Quận 1, TP.HCM",
          managers: ["user2"],
          created_at: new Date("2023-02-10"),
          totalRooms: 15,
          availableRooms: 2,
          occupiedRooms: 10,
          maintenanceRooms: 3,
          status: "maintenance",
          image:
            "https://s3-media0.fl.yelpcdn.com/bphoto/NRrhdd_U5K6L63AMmBEoHQ/348s.jpg",
        },
      ];

      const allMotels = [...processedMotels, ...additionalMotels];
      setMotels(allMotels);
      setFilteredMotels(allMotels);
      setLoading(false);
    }, 1000);
  }, []);

  // Filter motels
  useEffect(() => {
    if (searchQuery.trim() === "") {
      setFilteredMotels(motels);
    } else {
      const filtered = motels.filter(
        (motel) =>
          motel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          motel.address.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredMotels(filtered);
    }
  }, [searchQuery, motels]);

  const handleMotelPress = (motelId: string) => {};

  const handleAddMotel = () => {};

  // Get status text and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case "active":
        return { text: "Đang hoạt động", color: Colors.SUCCESS };
      case "construction":
        return { text: "Đang thi công", color: Colors.WARNING };
      case "maintenance":
        return { text: "Đang sửa chữa", color: Colors.DANGER };
      default:
        return { text: "Không xác định", color: Colors.GRAY_DARK };
    }
  };

  const renderMotelCard = ({ item }: { item: ExtendedMotel }) => {
    const statusInfo = getStatusInfo(item.status);

    return (
      <Surface style={styles.motelCard} elevation={2}>
        <TouchableOpacity
          style={styles.motelCardContent}
          onPress={() => handleMotelPress(item.motel_id)}
        >
          <Image source={{ uri: item.image }} style={styles.motelImage} />

          <View style={styles.motelInfo}>
            <View style={styles.motelHeader}>
              <Text style={styles.motelName} numberOfLines={1}>
                {item.name}
              </Text>
              <Chip
                style={[
                  styles.statusChip,
                  { backgroundColor: statusInfo.color },
                ]}
                textStyle={styles.statusChipText}
              >
                {statusInfo.text}
              </Chip>
            </View>

            <Text style={styles.motelAddress} numberOfLines={2}>
              <MaterialCommunityIcons
                name="map-marker"
                size={14}
                color={Colors.TEXT_SECONDARY}
              />{" "}
              {item.address}
            </Text>

            <Divider style={styles.divider} />

            <View style={styles.roomStats}>
              <View style={styles.roomStat}>
                <Text style={styles.roomStatNumber}>{item.totalRooms}</Text>
                <Text style={styles.roomStatLabel}>Tổng phòng</Text>
              </View>

              <View style={styles.roomStat}>
                <Text
                  style={[styles.roomStatNumber, { color: Colors.AVAILABLE }]}
                >
                  {item.availableRooms}
                </Text>
                <Text style={styles.roomStatLabel}>Trống</Text>
              </View>

              <View style={styles.roomStat}>
                <Text
                  style={[styles.roomStatNumber, { color: Colors.OCCUPIED }]}
                >
                  {item.occupiedRooms}
                </Text>
                <Text style={styles.roomStatLabel}>Đã thuê</Text>
              </View>

              <View style={styles.roomStat}>
                <Text
                  style={[styles.roomStatNumber, { color: Colors.MAINTENANCE }]}
                >
                  {item.maintenanceRooms}
                </Text>
                <Text style={styles.roomStatLabel}>Sửa chữa</Text>
              </View>
            </View>
          </View>
        </TouchableOpacity>
      </Surface>
    );
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />

      {/* Header */}
      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            iconColor={Colors.WHITE}
            size={24}
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
          <Text variant="headlineSmall" style={styles.headerTitle}>
            Quản lý nhà trọ
          </Text>
        </View>
      </Surface>

      {/* Search bar */}
      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Tìm kiếm nhà trọ..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
          iconColor={Colors.PRIMARY}
          elevation={2}
        />
      </View>

      {loading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.PRIMARY} />
          <Text style={styles.loadingText}>Đang tải dữ liệu...</Text>
        </View>
      ) : (
        <>
          <FlatList
            data={filteredMotels}
            keyExtractor={(item) => item.motel_id}
            renderItem={renderMotelCard}
            contentContainerStyle={styles.listContent}
            ItemSeparatorComponent={() => <View style={styles.separator} />}
            ListEmptyComponent={() => (
              <Surface style={styles.emptyContainer} elevation={0}>
                <MaterialCommunityIcons
                  name="home-remove"
                  size={48}
                  color={Colors.GRAY_DARK}
                />
                <Text style={styles.emptyText}>Không tìm thấy nhà trọ nào</Text>
              </Surface>
            )}
          />

          {/* Add motel button */}
          <FAB
            icon="plus"
            style={styles.fab}
            color={Colors.WHITE}
            onPress={handleAddMotel}
          />
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 16,
    paddingBottom: 16,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.WHITE,
    borderBottomWidth: 1,
    borderBottomColor: Colors.GRAY_MEDIUM,
  },
  searchBar: {
    backgroundColor: Colors.WHITE,
    borderRadius: 8,
  },
  listContent: {
    padding: 16,
    paddingBottom: 80, // Extra padding for FAB
  },
  separator: {
    height: 16,
  },
  motelCard: {
    borderRadius: 12,
    overflow: "hidden",
  },
  motelCardContent: {
    flexDirection: "column",
  },
  motelImage: {
    height: 150,
    width: "100%",
    resizeMode: "cover",
  },
  motelInfo: {
    padding: 16,
  },
  motelHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  motelName: {
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    flex: 1,
    marginRight: 8,
  },
  statusChip: {
    height: 28,
  },
  statusChipText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.sm,
    fontWeight: Typography.FONT_WEIGHT.medium,
  },
  motelAddress: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    marginBottom: 12,
  },
  divider: {
    marginVertical: 12,
  },
  roomStats: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  roomStat: {
    alignItems: "center",
  },
  roomStatNumber: {
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
  },
  roomStatLabel: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    marginTop: 4,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    marginTop: 12,
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
  },
  emptyContainer: {
    padding: 24,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.WHITE,
    borderRadius: 12,
    marginTop: 24,
  },
  emptyText: {
    marginTop: 12,
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
  },
  fab: {
    position: "absolute",
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.PRIMARY,
  },
});
