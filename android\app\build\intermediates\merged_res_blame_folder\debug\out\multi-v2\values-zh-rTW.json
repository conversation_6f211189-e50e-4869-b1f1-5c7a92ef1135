{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,832,924,1018,1115,1211,1306,1400,1496,1588,1680,1772,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "195,288,388,470,567,675,752,827,919,1013,1110,1206,1301,1395,1491,1583,1675,1767,1845,1941,2036,2131,2228,2324,2422,2572,2666,2745"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "297,392,485,585,667,764,872,949,1024,1116,1210,1307,1403,1498,1592,1688,1780,1872,1964,2042,2138,2233,2328,2425,2521,2619,2769,9267", "endColumns": "94,92,99,81,96,107,76,74,91,93,96,95,94,93,95,91,91,91,77,95,94,94,96,95,97,149,93,78", "endOffsets": "387,480,580,662,759,867,944,1019,1111,1205,1302,1398,1493,1587,1683,1775,1867,1959,2037,2133,2228,2323,2420,2516,2614,2764,2858,9341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "39,40,41,42,43,44,45,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3269,3361,3460,3554,3648,3741,3834,9935", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "3356,3455,3549,3643,3736,3829,3925,10031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,139,231,332", "endColumns": "83,91,100,92", "endOffsets": "134,226,327,420"}, "to": {"startLines": "52,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4456,4680,4772,4873", "endColumns": "83,91,100,92", "endOffsets": "4535,4767,4868,4961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,440,510,587,681,788,861,923,1001,1061,1121,1199,1260,1318,1374,1434,1492,1546,1631,1687,1745,1799,1864,1956,2030,2107,2227,2290,2353,2430,2480,2531,2597,2660,2728,2806,2867,2938,3005,3067,3146,3211,3294,3379,3453,3517,3593,3641,3705,3781,3859,3921,3985,4048,4128,4205,4281,4358,4412,4467", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "242,306,368,435,505,582,676,783,856,918,996,1056,1116,1194,1255,1313,1369,1429,1487,1541,1626,1682,1740,1794,1859,1951,2025,2102,2222,2285,2348,2425,2475,2526,2592,2655,2723,2801,2862,2933,3000,3062,3141,3206,3289,3374,3448,3512,3588,3636,3700,3776,3854,3916,3980,4043,4123,4200,4276,4353,4407,4462,4531"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,2993,3055,3122,3192,4110,4204,4311,4540,4602,5033,5234,5359,5437,5498,5556,5612,5672,5730,5784,5869,5925,5983,6037,6102,6402,6476,6553,6673,6736,6799,6876,6926,6977,7043,7106,7174,7252,7313,7384,7451,7513,7592,7657,7740,7825,7899,7963,8039,8087,8151,8227,8305,8367,8431,8494,8574,8651,8727,8804,8858,8913", "endLines": "5,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,63,61,66,69,76,93,106,72,61,77,59,59,77,60,57,55,59,57,53,84,55,57,53,64,91,73,76,119,62,62,76,49,50,65,62,67,77,60,70,66,61,78,64,82,84,73,63,75,47,63,75,77,61,63,62,79,76,75,76,53,54,68", "endOffsets": "292,2988,3050,3117,3187,3264,4199,4306,4379,4597,4675,5088,5289,5432,5493,5551,5607,5667,5725,5779,5864,5920,5978,6032,6097,6189,6471,6548,6668,6731,6794,6871,6921,6972,7038,7101,7169,7247,7308,7379,7446,7508,7587,7652,7735,7820,7894,7958,8034,8082,8146,8222,8300,8362,8426,8489,8569,8646,8722,8799,8853,8908,8977"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,121,218,301,373,440,506,581,646,712,782,854,927,1002,1069,1139,1212,1284,1361,1437,1509,1579,1648,1728,1796,1866,1933", "endColumns": "65,96,82,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "116,213,296,368,435,501,576,641,707,777,849,922,997,1064,1134,1207,1279,1356,1432,1504,1574,1643,1723,1791,1861,1928,1997"}, "to": {"startLines": "33,46,47,51,58,60,61,63,77,78,79,117,118,119,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2863,3930,4027,4384,4966,5093,5159,5294,6194,6260,6330,8982,9055,9130,9197,9346,9419,9491,9568,9644,9716,9786,9855,10036,10104,10174,10241", "endColumns": "65,96,82,71,66,65,74,64,65,69,71,72,74,66,69,72,71,76,75,71,69,68,79,67,69,66,68", "endOffsets": "2924,4022,4105,4451,5028,5154,5229,5354,6255,6325,6397,9050,9125,9192,9262,9414,9486,9563,9639,9711,9781,9850,9930,10099,10169,10236,10305"}}]}]}