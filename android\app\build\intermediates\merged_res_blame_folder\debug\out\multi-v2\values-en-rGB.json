{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-en-rGB/values-en-rGB.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,154,230,313,387,463,545,625,703,783,857", "endColumns": "98,75,82,73,75,81,79,77,79,73,73", "endOffsets": "149,225,308,382,458,540,620,698,778,852,926"}, "to": {"startLines": "45,49,57,72,73,111,112,113,115,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4105,4496,5197,6287,6361,9306,9388,9468,9629,9810,9884", "endColumns": "98,75,82,73,75,81,79,77,79,73,73", "endOffsets": "4199,4567,5275,6356,6432,9383,9463,9541,9704,9879,9953"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,426,526,634,718,818,933,1011,1086,1177,1270,1365,1459,1559,1652,1747,1841,1932,2023,2105,2208,2311,2410,2515,2619,2723,2879,9546", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "421,521,629,713,813,928,1006,1081,1172,1265,1360,1454,1554,1647,1742,1836,1927,2018,2100,2203,2306,2405,2510,2614,2718,2874,2974,9624"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3386,3482,3584,3683,3782,3886,3989,9709", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "3477,3579,3678,3777,3881,3984,4100,9805"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,354,432,509,595,679,777,892,971,1036,1126,1193,1252,1342,1406,1470,1533,1602,1666,1720,1832,1890,1952,2006,2078,2200,2287,2368,2508,2585,2666,2757,2811,2862,2928,2998,3075,3162,3233,3310,3379,3448,3539,3611,3700,3789,3863,3935,4021,4071,4137,4217,4301,4363,4427,4490,4590,4687,4779,4878,4936,4991", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,77", "endOffsets": "267,349,427,504,590,674,772,887,966,1031,1121,1188,1247,1337,1401,1465,1528,1597,1661,1715,1827,1885,1947,2001,2073,2195,2282,2363,2503,2580,2661,2752,2806,2857,2923,2993,3070,3157,3228,3305,3374,3443,3534,3606,3695,3784,3858,3930,4016,4066,4132,4212,4296,4358,4422,4485,4585,4682,4774,4873,4931,4986,5064"}, "to": {"startLines": "2,33,34,35,36,37,46,47,48,51,52,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2979,3061,3139,3216,3302,4204,4302,4417,4670,4735,5130,5280,5339,5429,5493,5557,5620,5689,5753,5807,5919,5977,6039,6093,6165,6437,6524,6605,6745,6822,6903,6994,7048,7099,7165,7235,7312,7399,7470,7547,7616,7685,7776,7848,7937,8026,8100,8172,8258,8308,8374,8454,8538,8600,8664,8727,8827,8924,9016,9115,9173,9228", "endLines": "5,33,34,35,36,37,46,47,48,51,52,56,58,59,60,61,62,63,64,65,66,67,68,69,70,71,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "endColumns": "12,81,77,76,85,83,97,114,78,64,89,66,58,89,63,63,62,68,63,53,111,57,61,53,71,121,86,80,139,76,80,90,53,50,65,69,76,86,70,76,68,68,90,71,88,88,73,71,85,49,65,79,83,61,63,62,99,96,91,98,57,54,77", "endOffsets": "317,3056,3134,3211,3297,3381,4297,4412,4491,4730,4820,5192,5334,5424,5488,5552,5615,5684,5748,5802,5914,5972,6034,6088,6160,6282,6519,6600,6740,6817,6898,6989,7043,7094,7160,7230,7307,7394,7465,7542,7611,7680,7771,7843,7932,8021,8095,8167,8253,8303,8369,8449,8533,8595,8659,8722,8822,8919,9011,9110,9168,9223,9301"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-en-rGB\\values-en-rGB.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "50,53,54,55", "startColumns": "4,4,4,4", "startOffsets": "4572,4825,4922,5031", "endColumns": "97,96,108,98", "endOffsets": "4665,4917,5026,5125"}}]}]}