<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <integer name="react_native_inspector_proxy_port">8081</integer>
    <string name="app_name">client</string>
    <string name="default_web_client_id" translatable="false">728339198977-qnipi0l8mj5lcblnq1gm1pd1kupse12p.apps.googleusercontent.com</string>
    <string name="expo_runtime_version">1.0.0</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="gcm_defaultSenderId" translatable="false">728339198977</string>
    <string name="google_api_key" translatable="false">AIzaSyBuildPlaceholderKeyForDevelopment</string>
    <string name="google_app_id" translatable="false">1:728339198977:android:abc123def456789</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBuildPlaceholderKeyForDevelopment</string>
    <string name="google_storage_bucket" translatable="false">quanlynhatro-8afcf.appspot.com</string>
    <string name="project_id" translatable="false">quanlynhatro-8afcf</string>
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
    <item name="android:textColor">@android:color/black</item>
    <item name="android:editTextStyle">@style/ResetEditText</item>
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
  </style>
    <style name="ResetEditText" parent="@android:style/Widget.EditText">
    <item name="android:padding">0dp</item>
    <item name="android:textColorHint">#c8c8c8</item>
    <item name="android:textColor">@android:color/black</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="AppTheme">
    <item name="android:windowBackground">@drawable/splashscreen</item>
  </style>
</resources>