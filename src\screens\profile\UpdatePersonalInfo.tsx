import React, { useState } from "react";
import {
  View,
  StyleSheet,
  ScrollView,
  StatusBar,
  Image,
  TouchableOpacity,
  Alert,
} from "react-native";
import {
  Text,
  Surface,
  Button,
  TextInput,
  IconButton,
} from "react-native-paper";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { RootStackParamList } from "../../types";
import { Colors, Typography } from "../../theme";

export default function UpdatePersonalInfo() {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();

  const [userData, setUserData] = useState({
    name: "An",
    email: "<EMAIL>",
    phone: "0912345678",
    role: "Quản lý",
    avatar: "https://randomuser.me/api/portraits/men/32.jpg",
  });

  const [name, setName] = useState(userData.name);
  const [email, setEmail] = useState(userData.email);
  const [phone, setPhone] = useState(userData.phone);
  const [avatar, setAvatar] = useState(userData.avatar);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({
    name: "",
    email: "",
    phone: "",
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = { name: "", email: "", phone: "" };

    if (!name.trim()) {
      newErrors.name = "Tên không được để trống";
      isValid = false;
    }

    if (!email.trim()) {
      newErrors.email = "Email không được để trống";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = "Email không hợp lệ";
      isValid = false;
    }

    if (!phone.trim()) {
      newErrors.phone = "Số điện thoại không được để trống";
      isValid = false;
    } else if (!/^[0-9]{10}$/.test(phone)) {
      newErrors.phone = "Số điện thoại phải có 10 chữ số";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleSelectImage = () => {};

  const handleSubmit = () => {
    if (validateForm()) {
      setIsLoading(true);

      setTimeout(() => {
        setUserData({
          ...userData,
          name,
          email,
          phone,
          avatar,
        });

        setIsLoading(false);
        Alert.alert("Thành công", "Thông tin cá nhân đã được cập nhật");
        navigation.goBack();
      }, 600);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={Colors.PRIMARY} barStyle="light-content" />

      <Surface style={styles.header} elevation={2}>
        <View style={styles.headerContent}>
          <IconButton
            icon="arrow-left"
            iconColor={Colors.WHITE}
            size={24}
            onPress={() => navigation.goBack()}
            style={styles.backButton}
          />
          <Text variant="headlineSmall" style={styles.headerTitle}>
            Cập nhật thông tin
          </Text>
        </View>
      </Surface>

      <ScrollView style={styles.content}>
        <View style={styles.avatarContainer}>
          <Image source={{ uri: avatar }} style={styles.avatar} />
          <TouchableOpacity
            style={styles.editAvatarButton}
            onPress={handleSelectImage}
          >
            <MaterialCommunityIcons
              name="camera"
              size={20}
              color={Colors.WHITE}
            />
          </TouchableOpacity>
        </View>

        {/* Form */}
        <Surface style={styles.formCard} elevation={1}>
          <TextInput
            label="Họ và tên"
            value={name}
            onChangeText={setName}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
            left={<TextInput.Icon icon="account" />}
          />
          {errors.name ? (
            <Text style={styles.errorText}>{errors.name}</Text>
          ) : null}

          <TextInput
            label="Email"
            value={email}
            onChangeText={setEmail}
            mode="outlined"
            style={styles.input}
            error={!!errors.email}
            keyboardType="email-address"
            left={<TextInput.Icon icon="email" />}
          />
          {errors.email ? (
            <Text style={styles.errorText}>{errors.email}</Text>
          ) : null}

          <TextInput
            label="Số điện thoại"
            value={phone}
            onChangeText={setPhone}
            mode="outlined"
            style={styles.input}
            error={!!errors.phone}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />
          {errors.phone ? (
            <Text style={styles.errorText}>{errors.phone}</Text>
          ) : null}

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={handleSubmit}
              style={styles.submitButton}
              icon="content-save"
              loading={isLoading}
              disabled={isLoading}
            >
              Lưu thay đổi
            </Button>

            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              style={styles.cancelButton}
              icon="close-circle"
              disabled={isLoading}
            >
              Hủy
            </Button>
          </View>
        </Surface>

        <View style={styles.noteContainer}>
          <Text style={styles.noteText}>
            * Lưu ý: Thông tin cá nhân của bạn sẽ được bảo mật theo chính sách
            của chúng tôi.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.GRAY_LIGHT,
  },
  header: {
    backgroundColor: Colors.PRIMARY,
    paddingTop: 12,
    paddingBottom: 16,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
  },
  backButton: {
    marginRight: 8,
  },
  headerTitle: {
    color: Colors.WHITE,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  avatarContainer: {
    alignItems: "center",
    marginVertical: 24,
    position: "relative",
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: Colors.WHITE,
  },
  editAvatarButton: {
    position: "absolute",
    bottom: 0,
    right: "35%",
    backgroundColor: Colors.PRIMARY,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: Colors.WHITE,
  },
  formCard: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  input: {
    marginBottom: 8,
    backgroundColor: Colors.WHITE,
  },
  errorText: {
    color: Colors.DANGER,
    fontSize: Typography.FONT_SIZE.sm,
    marginBottom: 8,
    marginLeft: 8,
  },
  buttonContainer: {
    marginTop: 16,
    gap: 12,
  },
  submitButton: {
    backgroundColor: Colors.PRIMARY,
    paddingVertical: 8,
  },
  cancelButton: {
    borderColor: Colors.PRIMARY,
  },
  noteContainer: {
    marginVertical: 16,
    paddingHorizontal: 8,
  },
  noteText: {
    fontSize: Typography.FONT_SIZE.sm,
    color: Colors.TEXT_SECONDARY,
    fontStyle: "italic",
  },
});
