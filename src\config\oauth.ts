/**
 * OAuth Configuration for Production and Development
 */

export const OAUTH_CONFIG = {
  // Google OAuth Client IDs
  GOOGLE: {
    // expo-auth-session (Web Client ID from Google Cloud Console)
    WEB_CLIENT_ID:
      "728339198977-qnjql08mg8lcklnq1gm1p4fkupsee12p.apps.googleusercontent.com",

    // google-services.json (Android)
    ANDROID_CLIENT_ID:
      "728339198977-5co0c0c57ldu7e1q5qm34bl23a402hvf.apps.googleusercontent.com",

    // GoogleService-Info.plist (iOS)
    IOS_CLIENT_ID:
      "728339198977-ingr1vt3edjkdu1fqu4mcaomjkeq0g4v.apps.googleusercontent.com",
  },

  REDIRECT_URIS: {
    DEVELOPMENT: "", // Auto-generated
    PRODUCTION: "", // Auto-generated
    LOCAL: "", // Auto-generated
  },

  // OAuth Scopes
  SCOPES: ["openid", "profile", "email"],
};

export const getGoogleClientId = (): string => {
  return OAUTH_CONFIG.GOOGLE.WEB_CLIENT_ID;
};

export const getRedirectUri = (): string => {
  // For development, this will be automatically handled by AuthSession.makeRedirectUri()
  return OAUTH_CONFIG.REDIRECT_URIS.PRODUCTION;
};

export const isProductionBuild = (): boolean => {
  return !__DEV__;
};

export const getOAuthScopes = (): string[] => {
  return OAUTH_CONFIG.SCOPES;
};
