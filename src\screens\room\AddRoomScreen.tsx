import React, { useState } from "react";
import {
  ScrollView,
  StyleSheet,
  View,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import {
  Button,
  TextInput,
  Text,
  Chip,
  Surface,
  Icon,
  Divider,
  TouchableRipple,
  Dialog,
  Portal,
  HelperText,
  Paragraph,
  IconButton,
} from "react-native-paper";
import { useNavigation } from "@react-navigation/native";
import type { NativeStackNavigationProp } from "@react-navigation/native-stack";
import type { RootStackParamList, Room } from "../../types";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";
import { addRoom } from "../../services/roomService";

const ROOM_TYPES = [
  { id: "1", labelKey: "roomTypes.single" },
  { id: "2", labelKey: "roomTypes.double" },
  { id: "3", labelKey: "roomTypes.withLoft" },
  { id: "4", labelKey: "roomTypes.withoutLoft" },
  { id: "5", labelKey: "roomTypes.studio" },
];

const AMENITIES = [
  { id: "1", labelKey: "amenities.airConditioner", icon: "air-conditioner" },
  { id: "2", labelKey: "amenities.refrigerator", icon: "fridge-outline" },
  { id: "3", labelKey: "amenities.washingMachine", icon: "washing-machine" },
  { id: "4", labelKey: "amenities.tv", icon: "television" },
  { id: "5", labelKey: "amenities.kitchen", icon: "stove" },
  { id: "6", labelKey: "amenities.balcony", icon: "window-open-variant" },
  { id: "7", labelKey: "amenities.wifi", icon: "wifi" },
  { id: "8", labelKey: "amenities.hotWater", icon: "water-boiler" },
  { id: "9", labelKey: "amenities.wardrobe", icon: "wardrobe-outline" },
  { id: "10", labelKey: "amenities.bed", icon: "bed-outline" },
];

interface AddRoomScreenProps {
  route?: {
    params?: {
      mode?: "add" | "edit";
      roomData?: Room;
    };
  };
}

export default function AddRoomScreen({ route }: AddRoomScreenProps) {
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const { colors, isDarkMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useLanguage();

  const mode = route?.params?.mode || "add";
  const existingRoom = route?.params?.roomData;

  const convertRoomTypesToIds = (loaiPhong: string[] = []) => {
    return loaiPhong
      .map((type) => {
        const roomType = ROOM_TYPES.find((rt) => rt.labelKey === type);
        return roomType ? roomType.id : type;
      })
      .filter(Boolean);
  };

  const convertAmenitiesToIds = (tienNghi: string[] = []) => {
    return tienNghi
      .map((amenity) => {
        const amenityType = AMENITIES.find((a) => a.labelKey === amenity);
        return amenityType ? amenityType.id : amenity;
      })
      .filter(Boolean);
  };

  const [roomData, setRoomData] = useState({
    room_number: existingRoom?.room_number || "",
    area: existingRoom?.dienTich?.toString() || "",
    note: existingRoom?.ghiChu || "",
    price: existingRoom?.gia?.toString() || "",
    electricity_price:
      (existingRoom as any)?.electricity_price?.toString() || "4000",
    water_price: (existingRoom as any)?.water_price?.toString() || "3000",
    status: existingRoom?.status || "available",
    room_types: convertRoomTypesToIds(existingRoom?.loaiPhong),
    amenities: convertAmenitiesToIds(existingRoom?.tienNghi),
  });

  const [bulkAddDialogVisible, setBulkAddDialogVisible] = useState(false);
  const [roomPrefix, setRoomPrefix] = useState("");
  const [roomSuffix, setRoomSuffix] = useState("");
  const [startNumber, setStartNumber] = useState("1");
  const [endNumber, setEndNumber] = useState("10");
  const [previewRooms, setPreviewRooms] = useState<string[]>([]);
  const [confirmBulkAddDialogVisible, setConfirmBulkAddDialogVisible] =
    useState(false);

  const [errors, setErrors] = useState<Record<string, string>>({});

  const [expandedSections, setExpandedSections] = useState({
    basicInfo: true,
    pricing: true,
    roomTypes: true,
    amenities: true,
  });

  const toggleSection = (section: keyof typeof expandedSections) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const resetBulkAddForm = () => {
    setRoomPrefix("");
    setRoomSuffix("");
    setStartNumber("1");
    setEndNumber("10");
    setPreviewRooms([]);
    setErrors({});
  };

  const generatePreviewRooms = () => {
    const start = parseInt(startNumber);
    const end = parseInt(endNumber);
    const newErrors: Record<string, string> = {};

    if (isNaN(start) || isNaN(end)) {
      newErrors.range = t("validation.mustBeNumber");
    } else if (start > end) {
      newErrors.range = t("validation.invalidRange");
    } else if (end - start > 100) {
      newErrors.range = t("validation.tooManyRooms");
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      const rooms = [];
      for (let i = start; i <= end; i++) {
        rooms.push(`${roomPrefix}${i}${roomSuffix}`);
      }
      setPreviewRooms(rooms);
    }

    return Object.keys(newErrors).length === 0;
  };

  const validateSingleRoomForm = () => {
    const newErrors: Record<string, string> = {};

    if (!roomData.room_number.trim()) {
      newErrors.roomNumber = t("validation.required");
    }

    if (!roomData.price.trim()) {
      newErrors.price = t("validation.required");
    } else if (isNaN(Number(roomData.price)) || Number(roomData.price) <= 0) {
      newErrors.price = t("validation.invalidPrice");
    }

    if (
      roomData.area.trim() &&
      (isNaN(Number(roomData.area)) || Number(roomData.area) <= 0)
    ) {
      newErrors.area = t("validation.invalidArea");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Mock update room function
  const handleUpdateRoom = async () => {
    try {
      setLoading(true);
      setError(null);

      Alert.alert(t("common.success"), t("room.updateSuccess"));
      navigation.goBack();
    } catch (error) {
      Alert.alert(t("common.error"), t("room.updateError"));
    } finally {
      setLoading(false);
    }
  };

  const handleSingleRoomSubmit = async () => {
    if (mode === "edit") {
      await handleUpdateRoom();
      return;
    }

    try {
      setLoading(true);
      setError(null);

      if (!roomData.room_number) {
        setError("Vui lòng nhập số phòng");
        return;
      }

      if (!roomData.price) {
        setError("Vui lòng nhập giá phòng");
        return;
      }

      const newRoom = {
        room_number: roomData.room_number,
        dienTich: Number(roomData.area) || 0,
        ghiChu: roomData.note || "",
        gia: Number(roomData.price) || 0,
        electricity_price: Number(roomData.electricity_price) || 0,
        water_price: Number(roomData.water_price) || 0,
        status: roomData.status as "available" | "occupied" | "maintenance",
        loaiPhong: roomData.room_types.map(
          (id: any) => ROOM_TYPES.find((type) => type.id === id)?.labelKey || ""
        ),
        tienNghi: roomData.amenities.map(
          (id: any) =>
            AMENITIES.find((amenity) => amenity.id === id)?.labelKey || ""
        ),
        motel_id: "",
        tienCoc: 0,
        tenant_id: null,
        created_at: new Date(),
        start_date: null,
        end_date: null,
        deleted: false,
      };

      const result = await addRoom(newRoom);

      if (result.success) {
        navigation.goBack();
      } else {
        setError("Có lỗi xảy ra khi thêm phòng. Vui lòng thử lại.");
      }
    } catch (err) {
      setError("Có lỗi xảy ra khi thêm phòng. Vui lòng thử lại.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleBulkRoomSubmit = () => {
    if (previewRooms.length === 0) {
      if (!generatePreviewRooms()) {
        return;
      }
    }
    setConfirmBulkAddDialogVisible(true);
  };

  const confirmBulkRoomAddition = () => {
    setConfirmBulkAddDialogVisible(false);
    setBulkAddDialogVisible(false);

    Alert.alert(
      t("common.success"),
      t("room.bulkAddSuccess", { count: previewRooms.length }),
      [{ text: t("common.done"), onPress: () => navigation.goBack() }]
    );
  };

  const toggleRoomType = (id: string) => {
    setRoomData((prev) => ({
      ...prev,
      room_types: prev.room_types.includes(id)
        ? prev.room_types.filter((type) => type !== id)
        : [...prev.room_types, id],
    }));
  };

  const toggleAmenity = (id: string) => {
    setRoomData((prev) => ({
      ...prev,
      amenities: prev.amenities.includes(id)
        ? prev.amenities.filter((amenity) => amenity !== id)
        : [...prev.amenities, id],
    }));
  };

  const renderSection = (
    titleKey: string,
    sectionKey: keyof typeof expandedSections,
    children: React.ReactNode
  ) => (
    <Surface
      style={[styles.section, { backgroundColor: colors.CARD_BACKGROUND }]}
      elevation={1}
    >
      <TouchableRipple onPress={() => toggleSection(sectionKey)}>
        <View style={styles.sectionHeader}>
          <Icon
            source={
              expandedSections[sectionKey] ? "chevron-down" : "chevron-right"
            }
            size={24}
            color="#006eff"
          />
          <Text
            variant="titleMedium"
            style={[styles.sectionTitle, { color: "#006eff" }]}
          >
            {t(titleKey)}
          </Text>
        </View>
      </TouchableRipple>
      {expandedSections[sectionKey] && (
        <>
          <Divider
            style={[styles.divider, { backgroundColor: colors.DIVIDER }]}
          />
          <View style={styles.sectionContent}>{children}</View>
        </>
      )}
    </Surface>
  );

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.BACKGROUND }]}
      behavior={Platform.OS === "ios" ? "padding" : "height"}
    >
      <Surface
        style={[styles.header, { backgroundColor: "#006eff" }]}
        elevation={2}
      >
        <Text variant="headlineSmall" style={{ color: colors.WHITE }}>
          {mode === "edit" ? t("room.editRoom") : t("room.addRoom")}
        </Text>
      </Surface>

      <ScrollView style={styles.form}>
        {renderSection(
          "room.roomInfo",
          "basicInfo",
          <>
            <TextInput
              mode="outlined"
              label={t("room.roomNumber")}
              placeholder={t("room.roomNumberPlaceholder")}
              value={roomData.room_number}
              onChangeText={(text) =>
                setRoomData({ ...roomData, room_number: text })
              }
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              left={<TextInput.Icon icon="door" />}
              error={!!errors.roomNumber}
            />
            {errors.roomNumber && (
              <HelperText type="error">{errors.roomNumber}</HelperText>
            )}

            <TextInput
              mode="outlined"
              label={t("room.areaWithUnit")}
              placeholder={t("room.areaPlaceholder")}
              value={roomData.area}
              onChangeText={(text) => setRoomData({ ...roomData, area: text })}
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              left={<TextInput.Icon icon="ruler-square" />}
              error={!!errors.area}
            />
            {errors.area && <HelperText type="error">{errors.area}</HelperText>}

            <TextInput
              mode="outlined"
              label={t("room.notes")}
              placeholder={t("room.notesPlaceholder")}
              value={roomData.note}
              onChangeText={(text) => setRoomData({ ...roomData, note: text })}
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              multiline
              numberOfLines={3}
              left={<TextInput.Icon icon="note-text" />}
            />
          </>
        )}

        {renderSection(
          "room.pricingAndServices",
          "pricing",
          <>
            <TextInput
              mode="outlined"
              label={t("room.roomPriceVND")}
              placeholder={t("room.pricePlaceholder")}
              value={roomData.price}
              onChangeText={(text) => setRoomData({ ...roomData, price: text })}
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              left={<TextInput.Icon icon="cash" />}
              error={!!errors.price}
            />
            {errors.price && (
              <HelperText type="error">{errors.price}</HelperText>
            )}

            <TextInput
              mode="outlined"
              label={t("room.electricityPriceUnit")}
              value={roomData.electricity_price}
              onChangeText={(text) =>
                setRoomData({ ...roomData, electricity_price: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              left={<TextInput.Icon icon="flash" />}
            />

            <TextInput
              mode="outlined"
              label={t("room.waterPriceUnit")}
              value={roomData.water_price}
              onChangeText={(text) =>
                setRoomData({ ...roomData, water_price: text })
              }
              keyboardType="numeric"
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              left={<TextInput.Icon icon="water" />}
            />
          </>
        )}

        {renderSection(
          "room.roomType",
          "roomTypes",
          <View style={styles.chipContainer}>
            {ROOM_TYPES.map((type) => (
              <Chip
                key={type.id}
                selected={roomData.room_types.includes(type.id)}
                onPress={() => toggleRoomType(type.id)}
                style={[
                  styles.chip,
                  {
                    backgroundColor: isDarkMode ? colors.GRAY_LIGHT : "#F0F0F0",
                  },
                  roomData.room_types.includes(type.id) && {
                    backgroundColor: "#006eff",
                  },
                ]}
                textStyle={[
                  { color: colors.TEXT_PRIMARY },
                  roomData.room_types.includes(type.id) && {
                    color: colors.WHITE,
                  },
                ]}
                showSelectedCheck
                icon="home-variant"
              >
                {t(type.labelKey)}
              </Chip>
            ))}
          </View>
        )}

        {renderSection(
          "room.amenities",
          "amenities",
          <View style={styles.amenitiesGrid}>
            {AMENITIES.map((amenity) => (
              <Chip
                key={amenity.id}
                selected={roomData.amenities.includes(amenity.id)}
                onPress={() => toggleAmenity(amenity.id)}
                style={[
                  styles.amenityChip,
                  {
                    backgroundColor: isDarkMode ? colors.GRAY_LIGHT : "#F0F0F0",
                  },
                  roomData.amenities.includes(amenity.id) && {
                    backgroundColor: "#006eff",
                  },
                ]}
                textStyle={[
                  { color: colors.TEXT_PRIMARY },
                  roomData.amenities.includes(amenity.id) && {
                    color: colors.WHITE,
                  },
                ]}
                showSelectedCheck
                icon={amenity.icon}
              >
                {t(amenity.labelKey)}
              </Chip>
            ))}
          </View>
        )}

        <Surface
          style={[
            styles.bulkAddContainer,
            { backgroundColor: colors.CARD_BACKGROUND },
          ]}
          elevation={1}
        >
          <View style={styles.bulkAddHeader}>
            <View>
              <Text style={[styles.bulkAddTitle, { color: "#006eff" }]}>
                {t("room.smartAdd")}
              </Text>
              <Text style={{ color: colors.TEXT_SECONDARY }}>
                {t("room.smartAddDescription")}
              </Text>
            </View>
            <IconButton
              icon="plus-box-multiple"
              size={24}
              iconColor="#006eff"
              onPress={() => {
                resetBulkAddForm();
                setBulkAddDialogVisible(true);
              }}
            />
          </View>
        </Surface>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSingleRoomSubmit}
            style={[styles.submitButton, { backgroundColor: "#006eff" }]}
            icon="check-circle"
          >
            {mode === "edit" ? t("common.update") : t("room.addRoomAction")}
          </Button>

          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.cancelButton, { borderColor: "#006eff" }]}
            icon="close-circle"
          >
            {t("common.cancel")}
          </Button>
        </View>
      </ScrollView>

      <Portal>
        <Dialog
          visible={bulkAddDialogVisible}
          onDismiss={() => setBulkAddDialogVisible(false)}
          style={{ backgroundColor: colors.CARD_BACKGROUND }}
        >
          <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
            {t("room.configureSmartAdd")}
          </Dialog.Title>
          <Dialog.Content>
            <Paragraph
              style={{ color: colors.TEXT_SECONDARY, marginBottom: 16 }}
            >
              {t("room.bulkCreationDescription")}
            </Paragraph>

            <TextInput
              mode="outlined"
              label={t("room.roomPrefix")}
              value={roomPrefix}
              onChangeText={setRoomPrefix}
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
              placeholder="A-"
            />

            <TextInput
              mode="outlined"
              label={t("room.roomSuffix")}
              value={roomSuffix}
              onChangeText={setRoomSuffix}
              style={[
                styles.input,
                { backgroundColor: colors.CARD_BACKGROUND },
              ]}
            />

            <View style={styles.formRow}>
              <View style={styles.formColumn}>
                <TextInput
                  mode="outlined"
                  label={t("room.startNumber")}
                  value={startNumber}
                  onChangeText={setStartNumber}
                  keyboardType="numeric"
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                />
              </View>

              <View style={styles.formColumn}>
                <TextInput
                  mode="outlined"
                  label={t("room.endNumber")}
                  value={endNumber}
                  onChangeText={setEndNumber}
                  keyboardType="numeric"
                  style={[
                    styles.input,
                    { backgroundColor: colors.CARD_BACKGROUND },
                  ]}
                />
              </View>
            </View>

            {errors.range && (
              <HelperText type="error">{errors.range}</HelperText>
            )}

            <Button
              mode="contained"
              onPress={generatePreviewRooms}
              style={{
                marginTop: 8,
                backgroundColor: "#006eff",
                marginBottom: 16,
              }}
            >
              {t("room.previewRooms")}
            </Button>

            {previewRooms.length > 0 && (
              <View style={{ marginTop: 8 }}>
                <Text
                  style={{
                    color: colors.TEXT_PRIMARY,
                    fontWeight: "bold",
                    marginBottom: 8,
                  }}
                >
                  {t("room.roomsToCreate")} {previewRooms.length}
                </Text>
                <View style={styles.chipContainer}>
                  {previewRooms.slice(0, 10).map((room, index) => (
                    <Chip
                      key={index}
                      style={[
                        styles.chip,
                        {
                          backgroundColor: isDarkMode
                            ? colors.GRAY_LIGHT
                            : "#F0F0F0",
                        },
                      ]}
                    >
                      {room}
                    </Chip>
                  ))}
                  {previewRooms.length > 10 && (
                    <Chip
                      style={[
                        styles.chip,
                        {
                          backgroundColor: isDarkMode
                            ? colors.GRAY_LIGHT
                            : "#F0F0F0",
                        },
                      ]}
                    >
                      +{previewRooms.length - 10} {t("room.rooms")}
                    </Chip>
                  )}
                </View>
              </View>
            )}
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setBulkAddDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              onPress={handleBulkRoomSubmit}
              mode="contained"
              style={{ backgroundColor: "#006eff" }}
              labelStyle={{ color: "#ffffff" }}
              disabled={previewRooms.length === 0}
            >
              {t("room.addMultipleRooms")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <Portal>
        <Dialog
          visible={confirmBulkAddDialogVisible}
          onDismiss={() => setConfirmBulkAddDialogVisible(false)}
          style={{ backgroundColor: colors.CARD_BACKGROUND }}
        >
          <Dialog.Title style={{ color: colors.TEXT_PRIMARY }}>
            {t("common.confirm")}
          </Dialog.Title>
          <Dialog.Content>
            <Paragraph style={{ color: colors.TEXT_SECONDARY }}>
              {t("room.bulkAddConfirmation")} {previewRooms.length}
            </Paragraph>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setConfirmBulkAddDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              onPress={confirmBulkRoomAddition}
              mode="contained"
              style={{ backgroundColor: "#006eff" }}
            >
              {t("common.confirm")}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 16,
    paddingTop: 48,
  },
  form: {
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    paddingBottom: 16,
  },
  sectionContent: {
    padding: 16,
    paddingTop: 0,
  },
  sectionTitle: {
    fontWeight: "bold",
    marginLeft: 8,
  },
  divider: {
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  chipContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  selectedChip: {
    backgroundColor: "#006eff",
  },
  selectedChipText: {
    color: "#FFFFFF",
  },
  amenitiesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 8,
  },
  amenityChip: {
    marginBottom: 8,
  },
  buttonContainer: {
    gap: 12,
    marginTop: 8,
    marginBottom: 24,
  },
  submitButton: {
    padding: 8,
  },
  cancelButton: {
    borderColor: "#006eff",
  },
  formRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 16,
  },
  formColumn: {
    flex: 1,
    marginRight: 8,
  },
  bulkAddContainer: {
    padding: 16,
    marginBottom: 16,
    borderRadius: 12,
  },
  bulkAddHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  bulkAddTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 8,
  },
});
