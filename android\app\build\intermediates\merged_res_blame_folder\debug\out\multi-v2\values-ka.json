{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-ka/values-ka.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,241,331,403,479,569,637,705,782,863,947,1027,1099,1187,1274,1353,1434,1514,1591,1669,1743,1827,1901,1981,2052", "endColumns": "74,110,89,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "125,236,326,398,474,564,632,700,777,858,942,1022,1094,1182,1269,1348,1429,1509,1586,1664,1738,1822,1896,1976,2047,2130"}, "to": {"startLines": "33,46,47,57,59,60,62,76,77,78,116,117,118,119,121,122,123,124,125,126,127,128,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3026,4248,4359,5349,5490,5566,5719,6738,6806,6883,9938,10022,10102,10174,10344,10431,10510,10591,10671,10748,10826,10900,11085,11159,11239,11310", "endColumns": "74,110,89,71,75,89,67,67,76,80,83,79,71,87,86,78,80,79,76,77,73,83,73,79,70,82", "endOffsets": "3096,4354,4444,5416,5561,5651,5782,6801,6878,6959,10017,10097,10169,10257,10426,10505,10586,10666,10743,10821,10895,10979,11154,11234,11305,11388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,557,661,779", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "146,248,347,446,552,656,774,875"}, "to": {"startLines": "39,40,41,42,43,44,45,129", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3524,3620,3722,3821,3920,4026,4130,10984", "endColumns": "95,101,98,98,105,103,117,100", "endOffsets": "3615,3717,3816,3915,4021,4125,4243,11080"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,316,427,513,618,731,814,893,984,1077,1172,1266,1366,1459,1554,1649,1740,1831,1912,2025,2131,2229,2342,2447,2551,2709,2808", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "208,311,422,508,613,726,809,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1907,2020,2126,2224,2337,2442,2546,2704,2803,2885"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,120", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "323,431,534,645,731,836,949,1032,1111,1202,1295,1390,1484,1584,1677,1772,1867,1958,2049,2130,2243,2349,2447,2560,2665,2769,2927,10262", "endColumns": "107,102,110,85,104,112,82,78,90,92,94,93,99,92,94,94,90,90,80,112,105,97,112,104,103,157,98,81", "endOffsets": "426,529,640,726,831,944,1027,1106,1197,1290,1385,1479,1579,1672,1767,1862,1953,2044,2125,2238,2344,2442,2555,2660,2764,2922,3021,10339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,273,351,425,509,602,696,795,920,1008,1075,1172,1241,1304,1391,1455,1521,1581,1650,1711,1765,1880,1939,1999,2053,2125,2255,2343,2427,2565,2643,2719,2813,2869,2923,2989,3062,3140,3226,3299,3377,3455,3530,3620,3695,3789,3887,3961,4038,4138,4191,4259,4348,4437,4499,4564,4627,4734,4832,4932,5031,5091,5149", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,93,55,53,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,59,57,79", "endOffsets": "268,346,420,504,597,691,790,915,1003,1070,1167,1236,1299,1386,1450,1516,1576,1645,1706,1760,1875,1934,1994,2048,2120,2250,2338,2422,2560,2638,2714,2808,2864,2918,2984,3057,3135,3221,3294,3372,3450,3525,3615,3690,3784,3882,3956,4033,4133,4186,4254,4343,4432,4494,4559,4622,4729,4827,4927,5026,5086,5144,5224"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3101,3179,3253,3337,3430,4449,4548,4673,4867,4934,5421,5656,5787,5874,5938,6004,6064,6133,6194,6248,6363,6422,6482,6536,6608,6964,7052,7136,7274,7352,7428,7522,7578,7632,7698,7771,7849,7935,8008,8086,8164,8239,8329,8404,8498,8596,8670,8747,8847,8900,8968,9057,9146,9208,9273,9336,9443,9541,9641,9740,9800,9858", "endLines": "5,34,35,36,37,38,48,49,50,52,53,58,61,63,64,65,66,67,68,69,70,71,72,73,74,75,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115", "endColumns": "12,77,73,83,92,93,98,124,87,66,96,68,62,86,63,65,59,68,60,53,114,58,59,53,71,129,87,83,137,77,75,93,55,53,65,72,77,85,72,77,77,74,89,74,93,97,73,76,99,52,67,88,88,61,64,62,106,97,99,98,59,57,79", "endOffsets": "318,3174,3248,3332,3425,3519,4543,4668,4756,4929,5026,5485,5714,5869,5933,5999,6059,6128,6189,6243,6358,6417,6477,6531,6603,6733,7047,7131,7269,7347,7423,7517,7573,7627,7693,7766,7844,7930,8003,8081,8159,8234,8324,8399,8493,8591,8665,8742,8842,8895,8963,9052,9141,9203,9268,9331,9438,9536,9636,9735,9795,9853,9933"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-ka\\values-ka.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,161,264,374", "endColumns": "105,102,109,104", "endOffsets": "156,259,369,474"}, "to": {"startLines": "51,54,55,56", "startColumns": "4,4,4,4", "startOffsets": "4761,5031,5134,5244", "endColumns": "105,102,109,104", "endOffsets": "4862,5129,5239,5344"}}]}]}