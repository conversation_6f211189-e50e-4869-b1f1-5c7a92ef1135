{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-km/values-km.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,347,423,503,582,661,761,873,953,1018,1112,1182,1244,1331,1394,1459,1518,1583,1644,1701,1820,1878,1939,1996,2067,2197,2283,2361,2499,2574,2645,2742,2797,2853,2919,2999,3089,3175,3254,3331,3401,3476,3564,3634,3734,3833,3907,3983,4090,4144,4217,4308,4404,4466,4530,4593,4692,4790,4882,4982,5040,5100", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,96,54,55,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,57,59,82", "endOffsets": "264,342,418,498,577,656,756,868,948,1013,1107,1177,1239,1326,1389,1454,1513,1578,1639,1696,1815,1873,1934,1991,2062,2192,2278,2356,2494,2569,2640,2737,2792,2848,2914,2994,3084,3170,3249,3326,3396,3471,3559,3629,3729,3828,3902,3978,4085,4139,4212,4303,4399,4461,4525,4588,4687,4785,4877,4977,5035,5095,5178"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3080,3158,3234,3314,3393,4387,4487,4599,4859,4924,5420,5644,5772,5859,5922,5987,6046,6111,6172,6229,6348,6406,6467,6524,6595,6947,7033,7111,7249,7324,7395,7492,7547,7603,7669,7749,7839,7925,8004,8081,8151,8226,8314,8384,8484,8583,8657,8733,8840,8894,8967,9058,9154,9216,9280,9343,9442,9540,9632,9732,9790,9850", "endLines": "5,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,77,75,79,78,78,99,111,79,64,93,69,61,86,62,64,58,64,60,56,118,57,60,56,70,129,85,77,137,74,70,96,54,55,65,79,89,85,78,76,69,74,87,69,99,98,73,75,106,53,72,90,95,61,63,62,98,97,91,99,57,59,82", "endOffsets": "314,3153,3229,3309,3388,3467,4482,4594,4674,4919,5013,5485,5701,5854,5917,5982,6041,6106,6167,6224,6343,6401,6462,6519,6590,6720,7028,7106,7244,7319,7390,7487,7542,7598,7664,7744,7834,7920,7999,8076,8146,8221,8309,8379,8479,8578,8652,8728,8835,8889,8962,9053,9149,9211,9275,9338,9437,9535,9627,9727,9785,9845,9928"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,128,234,322,405,477,547,631,697,766,842,919,1002,1082,1153,1230,1312,1389,1472,1554,1630,1701,1771,1864,1943,2017,2096", "endColumns": "72,105,87,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "123,229,317,400,472,542,626,692,761,837,914,997,1077,1148,1225,1307,1384,1467,1549,1625,1696,1766,1859,1938,2012,2091,2168"}, "to": {"startLines": "33,46,47,51,58,60,61,63,77,78,79,117,118,119,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3007,4193,4299,4679,5348,5490,5560,5706,6725,6794,6870,9933,10016,10096,10167,10328,10410,10487,10570,10652,10728,10799,10869,11063,11142,11216,11295", "endColumns": "72,105,87,82,71,69,83,65,68,75,76,82,79,70,76,81,76,82,81,75,70,69,92,78,73,78,76", "endOffsets": "3075,4294,4382,4757,5415,5555,5639,5767,6789,6865,6942,10011,10091,10162,10239,10405,10482,10565,10647,10723,10794,10864,10957,11137,11211,11290,11367"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,152,249,382", "endColumns": "96,96,132,99", "endOffsets": "147,244,377,477"}, "to": {"startLines": "52,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4762,5018,5115,5248", "endColumns": "96,96,132,99", "endOffsets": "4854,5110,5243,5343"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,253,351,451,552,664,776", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "145,248,346,446,547,659,771,872"}, "to": {"startLines": "39,40,41,42,43,44,45,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3472,3567,3670,3768,3868,3969,4081,10962", "endColumns": "94,102,97,99,100,111,111,100", "endOffsets": "3562,3665,3763,3863,3964,4076,4188,11058"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-km\\values-km.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,207,306,416,503,606,727,805,881,972,1065,1157,1251,1351,1444,1539,1633,1724,1815,1898,2002,2106,2206,2315,2424,2533,2695,2793", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "202,301,411,498,601,722,800,876,967,1060,1152,1246,1346,1439,1534,1628,1719,1810,1893,1997,2101,2201,2310,2419,2528,2690,2788,2872"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "319,421,520,630,717,820,941,1019,1095,1186,1279,1371,1465,1565,1658,1753,1847,1938,2029,2112,2216,2320,2420,2529,2638,2747,2909,10244", "endColumns": "101,98,109,86,102,120,77,75,90,92,91,93,99,92,94,93,90,90,82,103,103,99,108,108,108,161,97,83", "endOffsets": "416,515,625,712,815,936,1014,1090,1181,1274,1366,1460,1560,1653,1748,1842,1933,2024,2107,2211,2315,2415,2524,2633,2742,2904,3002,10323"}}]}]}