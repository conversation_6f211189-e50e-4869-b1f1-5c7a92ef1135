import React, { useState, useEffect, useRef } from "react";
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  TextInput as RNTextInput,
  Keyboard,
} from "react-native";
import { Text, ActivityIndicator } from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { useNavigation, useRoute, RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "@react-navigation/native-stack";
import { AuthStackParamList } from "../../types";
import { Colors, Spacing, Typography } from "../../theme";
import { useTheme } from "../../context/ThemeContext";
import { useLanguage } from "../../context/LanguageContext";
import { createUserWithEmailAndPassword, sendEmailVerification } from 'firebase/auth';
import { auth } from '../../../config/firebase';

type OTPVerificationRouteProp = RouteProp<AuthStackParamList, "OTPVerification">;

const OTPVerificationScreen = () => {
  const navigation =
    useNavigation<NativeStackNavigationProp<AuthStackParamList>>();
  const route = useRoute<OTPVerificationRouteProp>();
  const { isDarkMode, colors } = useTheme();
  const { t } = useLanguage();

  const { email, flow } = route.params || {
    email: "",
    flow: "signup",
  };

  const [otp, setOtp] = useState<string[]>(Array(6).fill(""));
  const [isLoading, setIsLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const [isVerified, setIsVerified] = useState(false);

  const inputRefs = useRef<Array<RNTextInput | null>>([]);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [timer]);

  const handleOtpChange = (text: string, index: number) => {
    if (text.length > 1) {
      text = text[0];
    }

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);

    if (text && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };
  const verifyOtp = async () => {
    Keyboard.dismiss();

    const otpString = otp.join("");
    if (otpString.length !== 6) {
      Alert.alert("Lỗi", "Vui lòng nhập đầy đủ mã OTP 6 chữ số");
      return;
    }

    // Kiểm tra OTP cho luồng đăng ký
    if (flow === "signup" && route.params?.registrationData) {
      const { verificationCode } = route.params.registrationData;
      if (otpString !== verificationCode) {
        Alert.alert("Lỗi", "Mã OTP không đúng");
        return;
      }
    }

    setIsLoading(true);

    try {
      if (flow === "signup" && route.params?.registrationData) {
        const { email, password } = route.params.registrationData;
        
        // Tạo tài khoản sau khi xác thực OTP thành công
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        const user = userCredential.user;
        
        // Gửi email xác thực
        await sendEmailVerification(user);
        
        setIsVerified(true);
        
        // Sign out user after registration
        await auth.signOut();
        
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{ name: "Login" }]
          });
          Alert.alert(
            "Đăng ký thành công",
            "Tài khoản của bạn đã được xác thực thành công. Vui lòng đăng nhập để tiếp tục."
          );
        }, 1000);
      } else {
        setIsVerified(true);
        setTimeout(() => {
          navigation.reset({
            index: 0,
            routes: [{ name: "Login" }]
          });
          Alert.alert(
            "Xác thực thành công",
            "Mật khẩu của bạn đã được đặt lại. Vui lòng kiểm tra email để nhận mật khẩu mới."
          );
        }, 1000);
      }
    } catch (error: any) {
      setIsLoading(false);
      let errorMessage = "Đã có lỗi xảy ra";
      if (error.code === 'auth/email-already-in-use') {
        errorMessage = "Email này đã được sử dụng";
      }
      Alert.alert("Lỗi", errorMessage);
    }
  };

  const resendOtp = () => {
    if (!canResend) return;

    if (flow === "signup" && route.params?.registrationData) {
      const newCode = Math.floor(100000 + Math.random() * 900000).toString();
      route.params.registrationData.verificationCode = newCode;
      console.log("New verification code:", newCode); // For testing
    }

    setCanResend(false);
    setTimer(60);

    Alert.alert("Đã gửi lại mã OTP", `Mã OTP mới đã được gửi đến ${email}`);
  };

  return (
    <View style={[styles.container, { backgroundColor: Colors.PRIMARY }]}>
      <SafeAreaView style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons
            name="arrow-left"
            size={24}
            color={Colors.WHITE}
          />
        </TouchableOpacity>
        <View style={styles.logoContainer}>
          <Image
            source={require("../../../assets/images/login.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>
      </SafeAreaView>

      <View
        style={[
          styles.formContainer,
          {
            backgroundColor: isDarkMode ? colors.CARD_BACKGROUND : Colors.WHITE,
          },
        ]}
      >
        {isVerified ? (
          <View style={styles.successContainer}>
            <MaterialCommunityIcons
              name="check-circle"
              size={60}
              color={Colors.SUCCESS}
              style={styles.successIcon}
            />
            <Text
              style={[
                styles.successTitle,
                {
                  color: isDarkMode ? colors.TEXT_PRIMARY : Colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("auth.otpVerificationSuccess")}
            </Text>
            <Text
              style={[
                styles.successText,
                {
                  color: isDarkMode
                    ? colors.TEXT_SECONDARY
                    : Colors.TEXT_SECONDARY,
                },
              ]}
            >
              {flow === "signup"
                ? t("auth.accountVerified")
                : t("auth.passwordReset")}
            </Text>
          </View>
        ) : (
          <>
            <Text
              style={[
                styles.title,
                {
                  color: isDarkMode ? colors.TEXT_PRIMARY : Colors.TEXT_PRIMARY,
                },
              ]}
            >
              {t("auth.otpVerification")}
            </Text>
            <Text
              style={[
                styles.description,
                {
                  color: isDarkMode
                    ? colors.TEXT_SECONDARY
                    : Colors.TEXT_SECONDARY,
                },
              ]}
            >
              {t("auth.enterOtpSent")}
            </Text>
            <Text style={[styles.email, { color: Colors.PRIMARY }]}>
              {email}
            </Text>

            <View style={styles.otpContainer}>
              {Array(6)
                .fill(0)
                .map((_, index) => (
                  <View
                    key={index}
                    style={[
                      styles.otpInputContainer,
                      {
                        borderColor: isDarkMode
                          ? colors.BORDER
                          : Colors.GRAY_MEDIUM,
                      },
                    ]}
                  >
                    <RNTextInput
                      ref={(ref) => (inputRefs.current[index] = ref)}
                      style={[
                        styles.otpInput,
                        {
                          color: isDarkMode
                            ? Colors.DARK.TEXT_PRIMARY
                            : Colors.TEXT_PRIMARY,
                          backgroundColor: isDarkMode
                            ? Colors.DARK.CARD_BACKGROUND
                            : Colors.WHITE,
                        },
                      ]}
                      value={otp[index]}
                      onChangeText={(text) => handleOtpChange(text, index)}
                      onKeyPress={(e) => handleKeyPress(e, index)}
                      keyboardType="number-pad"
                      maxLength={1}
                      selectTextOnFocus
                    />
                  </View>
                ))}
            </View>

            <TouchableOpacity
              style={[styles.verifyButton, { backgroundColor: Colors.PRIMARY }]}
              onPress={verifyOtp}
              disabled={isLoading || otp.join("").length !== 6}
            >
              {isLoading ? (
                <ActivityIndicator color={Colors.WHITE} size="small" />
              ) : (
                <Text style={styles.verifyButtonText}>{t("auth.verify")}</Text>
              )}
            </TouchableOpacity>

            <View style={styles.resendContainer}>
              <Text
                style={[
                  styles.resendText,
                  {
                    color: isDarkMode
                      ? colors.TEXT_SECONDARY
                      : Colors.TEXT_SECONDARY,
                  },
                ]}
              >
                {t("auth.noCodeReceived")} {canResend ? "" : `(${timer}s)`}
              </Text>
              <TouchableOpacity
                onPress={resendOtp}
                disabled={!canResend}
                style={!canResend && styles.disabledResend}
              >
                <Text
                  style={[
                    styles.resendLink,
                    { color: Colors.PRIMARY },
                    !canResend && styles.disabledResendText,
                  ]}
                >
                  {t("auth.resend")}
                </Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.PRIMARY,
  },
  header: {
    paddingTop: Spacing.SPACING.md,
    position: "relative",
  },
  backButton: {
    position: "absolute",
    top: Spacing.SPACING.md,
    left: Spacing.SPACING.md,
    zIndex: 10,
  },
  logoContainer: {
    alignItems: "center",
  },
  logo: {
    width: 200,
    height: 150,
  },
  formContainer: {
    flex: 1,
    backgroundColor: Colors.WHITE,
    borderTopLeftRadius: Spacing.BORDER_RADIUS.xl,
    borderTopRightRadius: Spacing.BORDER_RADIUS.xl,
    padding: Spacing.SPACING.md,
  },
  title: {
    fontSize: Typography.FONT_SIZE.xxl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    marginBottom: Spacing.SPACING.md,
    marginTop: Spacing.SPACING.sm,
    textAlign: "center",
  },
  description: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
  },
  email: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.PRIMARY,
    fontWeight: Typography.FONT_WEIGHT.bold,
    textAlign: "center",
    marginBottom: Spacing.SPACING.xl,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: Spacing.SPACING.xl,
  },
  otpInputContainer: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: Colors.GRAY_MEDIUM,
    borderRadius: Spacing.BORDER_RADIUS.sm,
    justifyContent: "center",
    alignItems: "center",
  },
  otpInput: {
    width: "100%",
    height: "100%",
    textAlign: "center",
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
  },
  verifyButton: {
    backgroundColor: Colors.PRIMARY,
    borderRadius: Spacing.BORDER_RADIUS.md,
    padding: Spacing.SPACING.md,
    alignItems: "center",
    marginBottom: Spacing.SPACING.md,
  },
  verifyButtonText: {
    color: Colors.WHITE,
    fontSize: Typography.FONT_SIZE.md,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  resendContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  resendText: {
    color: Colors.TEXT_SECONDARY,
    fontSize: Typography.FONT_SIZE.sm,
    marginRight: Spacing.SPACING.xs,
  },
  resendLink: {
    color: Colors.PRIMARY,
    fontSize: Typography.FONT_SIZE.sm,
    fontWeight: Typography.FONT_WEIGHT.bold,
  },
  disabledResend: {
    opacity: 0.5,
  },
  disabledResendText: {
    color: Colors.GRAY_DARK,
  },
  successContainer: {
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Spacing.SPACING.md,
  },
  successIcon: {
    marginBottom: Spacing.SPACING.md,
  },
  successTitle: {
    fontSize: Typography.FONT_SIZE.xl,
    fontWeight: Typography.FONT_WEIGHT.bold,
    color: Colors.TEXT_PRIMARY,
    marginBottom: Spacing.SPACING.sm,
  },
  successText: {
    fontSize: Typography.FONT_SIZE.md,
    color: Colors.TEXT_SECONDARY,
    textAlign: "center",
    marginBottom: Spacing.SPACING.xl,
  },
});

export default OTPVerificationScreen;
