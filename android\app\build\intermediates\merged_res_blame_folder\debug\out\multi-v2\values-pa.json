{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-pa/values-pa.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,263,340,419,500,599,688,796,908,991,1055,1147,1216,1275,1360,1423,1485,1543,1607,1668,1722,1836,1894,1954,2008,2078,2205,2286,2365,2500,2576,2653,2737,2792,2847,2913,2982,3059,3145,3213,3289,3359,3424,3519,3592,3686,3779,3853,3922,4016,4072,4139,4223,4311,4373,4437,4500,4597,4692,4783,4879,4938,4997", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "258,335,414,495,594,683,791,903,986,1050,1142,1211,1270,1355,1418,1480,1538,1602,1663,1717,1831,1889,1949,2003,2073,2200,2281,2360,2495,2571,2648,2732,2787,2842,2908,2977,3054,3140,3208,3284,3354,3419,3514,3587,3681,3774,3848,3917,4011,4067,4134,4218,4306,4368,4432,4495,4592,4687,4778,4874,4933,4992,5069"}, "to": {"startLines": "2,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3046,3123,3202,3283,3382,4408,4516,4628,4893,4957,5438,5656,5782,5867,5930,5992,6050,6114,6175,6229,6343,6401,6461,6515,6585,6929,7010,7089,7224,7300,7377,7461,7516,7571,7637,7706,7783,7869,7937,8013,8083,8148,8243,8316,8410,8503,8577,8646,8740,8796,8863,8947,9035,9097,9161,9224,9321,9416,9507,9603,9662,9721", "endLines": "5,34,35,36,37,38,48,49,50,53,54,59,62,64,65,66,67,68,69,70,71,72,73,74,75,76,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116", "endColumns": "12,76,78,80,98,88,107,111,82,63,91,68,58,84,62,61,57,63,60,53,113,57,59,53,69,126,80,78,134,75,76,83,54,54,65,68,76,85,67,75,69,64,94,72,93,92,73,68,93,55,66,83,87,61,63,62,96,94,90,95,58,58,76", "endOffsets": "308,3118,3197,3278,3377,3466,4511,4623,4706,4952,5044,5502,5710,5862,5925,5987,6045,6109,6170,6224,6338,6396,6456,6510,6580,6707,7005,7084,7219,7295,7372,7456,7511,7566,7632,7701,7778,7864,7932,8008,8078,8143,8238,8311,8405,8498,8572,8641,8735,8791,8858,8942,9030,9092,9156,9219,9316,9411,9502,9598,9657,9716,9793"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,239,330,407,478,547,627,694,761,835,911,994,1073,1141,1219,1302,1376,1460,1548,1623,1694,1765,1851,1920,1994,2063", "endColumns": "70,112,90,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "121,234,325,402,473,542,622,689,756,830,906,989,1068,1136,1214,1297,1371,1455,1543,1618,1689,1760,1846,1915,1989,2058,2131"}, "to": {"startLines": "33,46,47,51,58,60,61,63,77,78,79,117,118,119,120,122,123,124,125,126,127,128,129,131,132,133,134", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2975,4204,4317,4711,5367,5507,5576,5715,6712,6779,6853,9798,9881,9960,10028,10186,10269,10343,10427,10515,10590,10661,10732,10919,10988,11062,11131", "endColumns": "70,112,90,76,70,68,79,66,66,73,75,82,78,67,77,82,73,83,87,74,70,70,85,68,73,68,72", "endOffsets": "3041,4312,4403,4783,5433,5571,5651,5777,6774,6848,6924,9876,9955,10023,10101,10264,10338,10422,10510,10585,10656,10727,10813,10983,11057,11126,11199"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,358,459,561,659,788", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "148,250,353,454,556,654,783,884"}, "to": {"startLines": "39,40,41,42,43,44,45,130", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3471,3569,3671,3774,3875,3977,4075,10818", "endColumns": "97,101,102,100,101,97,128,100", "endOffsets": "3564,3666,3769,3870,3972,4070,4199,10914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,160,261,375", "endColumns": "104,100,113,102", "endOffsets": "155,256,370,473"}, "to": {"startLines": "52,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "4788,5049,5150,5264", "endColumns": "104,100,113,102", "endOffsets": "4888,5145,5259,5362"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-pa\\values-pa.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,305,410,496,596,709,787,864,955,1048,1142,1236,1336,1429,1524,1618,1709,1800,1879,1989,2092,2188,2299,2401,2511,2670,2767", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "203,300,405,491,591,704,782,859,950,1043,1137,1231,1331,1424,1519,1613,1704,1795,1874,1984,2087,2183,2294,2396,2506,2665,2762,2842"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,121", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "313,416,513,618,704,804,917,995,1072,1163,1256,1350,1444,1544,1637,1732,1826,1917,2008,2087,2197,2300,2396,2507,2609,2719,2878,10106", "endColumns": "102,96,104,85,99,112,77,76,90,92,93,93,99,92,94,93,90,90,78,109,102,95,110,101,109,158,96,79", "endOffsets": "411,508,613,699,799,912,990,1067,1158,1251,1345,1439,1539,1632,1727,1821,1912,2003,2082,2192,2295,2391,2502,2604,2714,2873,2970,10181"}}]}]}