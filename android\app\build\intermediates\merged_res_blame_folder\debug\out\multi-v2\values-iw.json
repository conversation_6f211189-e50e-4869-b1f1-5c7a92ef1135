{"logs": [{"outputFile": "com.datacoolie.quanlynhatro.app-mergeDebugResources-39:/values-iw/values-iw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\46e831abd82e09fbed4cbdd896e31662\\transformed\\browser-1.6.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "54,57,58,59", "startColumns": "4,4,4,4", "startOffsets": "4804,5048,5148,5254", "endColumns": "90,99,105,101", "endOffsets": "4890,5143,5249,5351"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\965ca604c3431eab8811bd7682895e8c\\transformed\\appcompat-1.6.1\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,310,418,502,604,720,799,877,968,1062,1156,1250,1350,1443,1538,1631,1722,1814,1895,2000,2103,2201,2306,2408,2510,2664,2761", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "205,305,413,497,599,715,794,872,963,1057,1151,1245,1345,1438,1533,1626,1717,1809,1890,1995,2098,2196,2301,2403,2505,2659,2756,2838"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "414,519,619,727,811,913,1029,1108,1186,1277,1371,1465,1559,1659,1752,1847,1940,2031,2123,2204,2309,2412,2510,2615,2717,2819,2973,10055", "endColumns": "104,99,107,83,101,115,78,77,90,93,93,93,99,92,94,92,90,91,80,104,102,97,104,101,101,153,96,81", "endOffsets": "514,614,722,806,908,1024,1103,1181,1272,1366,1460,1554,1654,1747,1842,1935,2026,2118,2199,2304,2407,2505,2610,2712,2814,2968,3065,10132"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\db3cb4c010cd7242b65007b624d40152\\transformed\\jetified-react-android-0.73.6-debug\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,221,308,386,456,525,606,674,742,820,898,980,1059,1130,1208,1288,1361,1441,1519,1594,1666,1738,1825,1896,1975,2044", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "119,216,303,381,451,520,601,669,737,815,893,975,1054,1125,1203,1283,1356,1436,1514,1589,1661,1733,1820,1891,1970,2039,2114"}, "to": {"startLines": "35,48,49,53,60,62,63,65,79,80,81,119,120,121,122,124,125,126,127,128,129,130,131,133,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3070,4256,4353,4726,5356,5496,5565,5709,6680,6748,6826,9745,9827,9906,9977,10137,10217,10290,10370,10448,10523,10595,10667,10855,10926,11005,11074", "endColumns": "68,96,86,77,69,68,80,67,67,77,77,81,78,70,77,79,72,79,77,74,71,71,86,70,78,68,74", "endOffsets": "3134,4348,4435,4799,5421,5560,5641,5772,6743,6821,6899,9822,9901,9972,10050,10212,10285,10365,10443,10518,10590,10662,10749,10921,11000,11069,11144"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\f1852cac82263a0db8af6cf304099aeb\\transformed\\material-1.9.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,364,441,516,593,693,784,877,990,1070,1135,1223,1293,1356,1448,1511,1571,1630,1693,1754,1808,1910,1967,2026,2080,2148,2259,2340,2422,2554,2625,2698,2786,2839,2893,2959,3032,3108,3194,3264,3339,3421,3489,3574,3644,3734,3825,3899,3972,4061,4112,4179,4261,4346,4408,4472,4535,4629,4724,4814,4910,4967,5025", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "359,436,511,588,688,779,872,985,1065,1130,1218,1288,1351,1443,1506,1566,1625,1688,1749,1803,1905,1962,2021,2075,2143,2254,2335,2417,2549,2620,2693,2781,2834,2888,2954,3027,3103,3189,3259,3334,3416,3484,3569,3639,3729,3820,3894,3967,4056,4107,4174,4256,4341,4403,4467,4530,4624,4719,4809,4905,4962,5020,5095"}, "to": {"startLines": "2,36,37,38,39,40,50,51,52,55,56,61,64,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3139,3216,3291,3368,3468,4440,4533,4646,4895,4960,5426,5646,5777,5869,5932,5992,6051,6114,6175,6229,6331,6388,6447,6501,6569,6904,6985,7067,7199,7270,7343,7431,7484,7538,7604,7677,7753,7839,7909,7984,8066,8134,8219,8289,8379,8470,8544,8617,8706,8757,8824,8906,8991,9053,9117,9180,9274,9369,9459,9555,9612,9670", "endLines": "7,36,37,38,39,40,50,51,52,55,56,61,64,66,67,68,69,70,71,72,73,74,75,76,77,78,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118", "endColumns": "12,76,74,76,99,90,92,112,79,64,87,69,62,91,62,59,58,62,60,53,101,56,58,53,67,110,80,81,131,70,72,87,52,53,65,72,75,85,69,74,81,67,84,69,89,90,73,72,88,50,66,81,84,61,63,62,93,94,89,95,56,57,74", "endOffsets": "409,3211,3286,3363,3463,3554,4528,4641,4721,4955,5043,5491,5704,5864,5927,5987,6046,6109,6170,6224,6326,6383,6442,6496,6564,6675,6980,7062,7194,7265,7338,7426,7479,7533,7599,7672,7748,7834,7904,7979,8061,8129,8214,8284,8374,8465,8539,8612,8701,8752,8819,8901,8986,9048,9112,9175,9269,9364,9454,9550,9607,9665,9740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6fa08ebee00822939d86a47ace4a1202\\transformed\\core-1.12.0\\res\\values-iw\\values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "41,42,43,44,45,46,47,132", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3559,3653,3755,3852,3949,4050,4150,10754", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "3648,3750,3847,3944,4045,4145,4251,10850"}}]}]}